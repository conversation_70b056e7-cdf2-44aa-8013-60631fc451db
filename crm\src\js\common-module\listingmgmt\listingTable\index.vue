<template>
    <DataTable
        v-if="tableData.length"
        class="LD-table table"
        :class="{
            'expired-lm-table': isExpiredListingType,
            'team-listings-sold': isTeamListingForClosed
        }"
        :data="tableData"
        :sort="tableSort"
        :noBorder="true"
        :rightFreeze="
            isUK || ((!isExpiredListingType || isHoldListingTab) && !isTeamListingForClosed) ? 1 : 0
        "
        @sortchange="setSort($event)"
        @infinityLoad="infinityLoad"
        :infinityLoadStatus="infinityLoadStatus"
    >
        <template>
            <DataTableColumn
                v-if="!isExpiredListingType && !isTeamListingForClosed"
                :width="36"
                textAlign="right"
                cellClass="check-box-cls"
                headerCellClass="check-box-cls"
            >
                <template v-slot:head>
                    <CheckBox
                        :checked="isSelectAll"
                        :beforeChange="selectAllBeforeChange"
                        @change="selectAllChange($event)"
                    ></CheckBox>
                </template>
                <template v-slot="{ data }">
                    <CheckBox
                        :checked="data.isSelected"
                        :beforeChange="selectBeforeChange"
                        @change="selectChange($event, data)"
                    ></CheckBox>
                </template>
            </DataTableColumn>
            <DataTableColumn :width="360" :label="$t('listingMgmt.listingTable.label.address')">
                <template v-slot="item">
                    <div class="house-introduction">
                        <img
                            class="house-preview click"
                            @click="showListingDetail(item.data)"
                            v-lazyload="$ft.img_compress(item.data.previewPicture, 128)"
                        />

                        <div
                            class="item-inner"
                            @click="showListingDetail(item.data)"
                            :title="item.data.address"
                        >
                            <div class="item-inner-address">
                                <div class="item-inner_street" v-if="item.data.streetAddress">
                                    {{ item.data.streetAddress }}
                                </div>

                                <div class="item-inner_address">
                                    {{ formatAddress(item.data) }}
                                </div>
                            </div>

                            <div
                                class="house-expend"
                                v-if="
                                    !(
                                        isTeamListingForClosed &&
                                        item.data?.multiFieldsJson?.chimePermissions ===
                                            'Participant Only Listing'
                                    )
                                "
                                @click.stop="showListingDetail(item.data)"
                            >
                                <i class="icon2017 icon-zoom_03"></i>
                            </div>
                        </div>

                        <div
                            :class="['house-status', formatListingStatus(item.data.listingStatus)]"
                            v-tip="{
                                content: item.data.listingStatus,
                                placement: 'bottom'
                            }"
                        >
                            <span></span>
                        </div>
                    </div>
                </template>
            </DataTableColumn>
            <DataTableColumn
                :width="150"
                :label="$t('listingMgmt.listingTable.label.purchaseType')"
            >
                <template v-slot="item">
                    <span>{{ item.data.purchaseType }}</span>
                </template>
            </DataTableColumn>
            <DataTableColumn
                :minWidth="170"
                :widthRatio="170"
                :label="$t('listingMgmt.listingTable.label.listingPrice')"
                :sortOrders="['asc', 'desc']"
                :sortGroup="[
                    {
                        label: $t('listingMgmt.listingTable.label.price'),
                        key: 'PRICE'
                    },
                    {
                        label: $t('listingMgmt.listingTable.label.priceChange'),
                        key: 'PRICE_CHANGE'
                    }
                ]"
            >
                <template v-slot="item">
                    <span class="list-price">{{ formatsPrice(item.data.price) }}</span
                    ><br />
                    <span
                        v-if="item.data.oldValue > 0 && item.data.oldValue !== item.data.price"
                        class="price-ratio"
                        :class="{
                            positive: item.data.positiveRatio
                        }"
                    >
                        <span
                            class="icon2017"
                            :class="{
                                'icon-arrow_04_down_1': !item.data.positiveRatio,
                                'icon-arrow_04_up_1': item.data.positiveRatio
                            }"
                        ></span>
                        {{ item.data.priceRatio }}
                    </span>
                </template>
            </DataTableColumn>
            <DataTableColumn
                :minWidth="150"
                :widthRatio="150"
                :label="$t('listingMgmt.listingTable.label.bedBath')"
                :sortOrders="['asc', 'desc']"
                :sortGroup="[
                    {
                        label: $t('listingMgmt.listingTable.label.Bedrooms'),
                        key: 'BEDROOMS'
                    },
                    {
                        label: $t('listingMgmt.listingTable.label.Bathrooms'),
                        key: 'BATHS'
                    }
                ]"
            >
                <template v-slot="item">
                    <span
                        class="house-params badbeth"
                        v-html="formatBedBath(item.data.bedrooms, item.data.bathrooms)"
                    />
                </template>
            </DataTableColumn>
            <DataTableColumn
                :minWidth="150"
                :widthRatio="150"
                :label="$t('listingMgmt.listingTable.label.sqft')"
                :sortOrders="['asc', 'desc']"
                sort="SQFT"
            >
                <template v-slot="item">
                    <span
                        class="house-params"
                        :class="{ notValid: !isValid(getHouseArea(item.data)) }"
                    >
                        {{ getHouseArea(item.data) }}
                    </span>
                </template>
            </DataTableColumn>
            <DataTableColumn
                :show="isSHowOpenHouseColumn"
                :minWidth="154"
                :widthRatio="154"
                :label="$t('listingMgmt.listingTable.label.openHouse')"
                :sortOrders="['asc', 'desc']"
                sort="OPEN_HOUSE_SCHEDULES"
            >
                <template v-slot="item">
                    <div class="open-house">
                        <p class="date">
                            {{ item.data.openHouseSchedule.date }}
                        </p>
                        <p class="time">
                            {{ item.data.openHouseSchedule.time }}
                        </p>
                        <p class="more" v-if="item.data.openHouseSchedule.list.length > 0">
                            <span
                                v-tip="{
                                    content: getOpenHouseScheduleTip(
                                        item.data.openHouseSchedule.list
                                    ),
                                    placement: 'bottom'
                                }"
                                class="icon2017 icon-more_01"
                            ></span>
                        </p>
                    </div>
                </template>
            </DataTableColumn>
            <DataTableColumn
                :minWidth="260"
                :widthRatio="260"
                :label="localizeListing($t('listingMgmt.listingTable.label.listingAgent'))"
                cellClass="valign-middle listing-agent"
            >
                <template v-slot="item">
                    <ListingAgent :listing="item.data" :actionStatus="actionStatus" />
                </template>
            </DataTableColumn>
            <DataTableColumn
                v-if="!(isExpiredListingType || isTeamListingForClosed)"
                :minWidth="180"
                :widthRatio="180"
                :label="$t('listingMgmt.listingTable.label.buyerMatching')"
                cellClass="valign-middle"
                :sortOrders="actionSortConfig.sortOrders"
                :sort="actionSortConfig.sort"
            >
                <template v-slot="item">
                    <p
                        class="view-matched"
                        v-lazy="{
                            load: loadMatchLead,
                            loadParam: {
                                source: item.data,
                                changeEl: true
                            }
                        }"
                        :key="item.data.id"
                        @click="openDialog(item.data)"
                    >
                        {{ $t("listingMgmt.listingTable.view") }}
                    </p>
                </template>
            </DataTableColumn>
            <DataTableColumn
                v-if="!isExpiredListingType"
                :minWidth="90"
                :widthRatio="90"
                :label="$t('listingMgmt.listingTable.label.view')"
                cellClass="activity"
            >
                <template v-slot="item">
                    <div class="row">
                        <span class="lead-active" v-tip="$t('listingMgmt.listingTable.tip6')">
                            <span class="icon2017 icon-view_01"></span>
                            <span class="number">{{ item.data.viewedCount }}</span>
                        </span>
                    </div>
                </template>
            </DataTableColumn>
            <DataTableColumn
                :minWidth="180"
                :widthRatio="135"
                :label="$t('listingMgmt.listingTable.label.daysOnMarket')"
                :sortOrders="['desc', 'asc']"
                sort="MLS_LIST_DATE_L"
                cellClass="normal-cell-text"
                headerCellClass="reverse-sort"
            >
                <template v-slot="item">
                    {{
                        isValid(item.data.daysOnList)
                            ? $t("listingMgmt.listingTable.onMarketDay", {
                                  count: item.data.daysOnList,
                                  countStr: $ft.number(item.data.daysOnList)
                              })
                            : $t("listingMgmt.listingTable.onMarketDay", {
                                  count: 0,
                                  countStr: 0
                              })
                    }}
                </template>
            </DataTableColumn>
            <DataTableColumn
                v-if="!isTeamListingForClosed"
                :minWidth="170"
                :widthRatio="170"
                :label="$t('listingMgmt.listingTable.label.updateTime')"
                :sortOrders="['asc', 'desc']"
                sort="LAST_PRIMARY_CHANGE_TIME"
            >
                <template v-slot="item">
                    <p class="updated-time">
                        {{ dealUpdateTime(item.data) }}
                    </p>
                    <p class="publish-status">
                        <span
                            class="mr10"
                            v-if="
                                !item.data?.multiFieldsJson?.isNotRLSAddress &&
                                item.data.rlsPublishStatus === 1
                            "
                        >
                            <i class="icon2017 icon-success_01 mr5" />
                            {{ $t("listingMgmt.litingCard.rls") }}
                        </span>
                        <span
                            class="mr10"
                            v-if="
                                !item.data?.multiFieldsJson?.isNotRLSAddress &&
                                item.data.rlsPublishStatus === 0
                            "
                        >
                            <i class="icon2017 icon-error mr5" />
                            {{ $t("listingMgmt.litingCard.unpublished") }}
                        </span>
                        <span
                            class="mr10"
                            v-if="
                                item.data?.multiFieldsJson?.isNotRLSAddress &&
                                item.data.mlsOrgId == 22000 &&
                                isRLSPermission
                            "
                        >
                            <template v-if="item.data.rlsPublishStatus === 1">
                                <i class="icon2017 icon-success_01 mr5 color-gray" />{{
                                    $t("listingMgmt.litingCard.nonRlsListing")
                                }}
                            </template>
                            <template v-else>
                                <i class="icon2017 icon-error mr5" />{{
                                    $t("listingMgmt.litingCard.nonRls")
                                }}
                            </template>
                        </span>
                        <span
                            v-if="
                                item.data.rlsPublishStatus === 1 &&
                                !isUK &&
                                item.data.multiFieldsJson?.chimeLuxTV
                            "
                        >
                            <i class="icon2017 icon-success_01 mr5" />
                            {{ $t("listingMgmt.litingCard.luxVT") }}
                        </span>
                        <span
                            v-if="
                                isRLSPermission &&
                                !isUK &&
                                item.data.multiFieldsJson?.chimePermissions ===
                                    'Participant Only Listing'
                            "
                            ><i class="icon2017 icon-success_01 mr5" />
                            {{ $t("listingMgmt.litingCard.participantOnly") }}
                        </span>
                    </p>
                </template>
            </DataTableColumn>
            <DataTableColumn
                v-if="
                    ((!isExpiredListingType || isHoldListingTab) && isTeamListingForClosed) || isUK
                "
                :minWidth="160"
                :widthRatio="200"
                textAlign="center"
                cellPadding="0"
                :label="$t('listingMgmt.listingTable.label.action')"
                cellClass="valign-middle center"
            >
                <template v-slot="item">
                    <div class="listing-table-action">
                        <template
                            v-if="
                                canEditDeleteUK(item.data) &&
                                (isHoldListingTab || isExpiredListingType)
                            "
                        >
                            <div
                                class="listing-table-action_cell"
                                :class="{ disabled: !canEditPocket(item.data) }"
                                v-tip="{
                                    content: canEditPocket(item.data)
                                        ? ''
                                        : $t('listingMgmt.listingTable.editTip'),
                                    placement: 'top',
                                    delay: 500,
                                    maxWidth: '320px'
                                }"
                                @click="editPocket(item.data)"
                            >
                                <i class="icon2017 icon-edit_01"></i>
                            </div>
                            <div
                                v-if="showOrCanDeletePocketEntry(item.data)"
                                class="listing-table-action_cell"
                                :class="{
                                    disabled: !showOrCanDeletePocketEntry(item.data)
                                }"
                                v-tip="{
                                    content: showOrCanDeletePocketEntry(item.data)
                                        ? ''
                                        : $t('listingMgmt.listingTable.deleteTip'),
                                    placement: 'top',
                                    delay: 500,
                                    maxWidth: '320px'
                                }"
                                @click="deletePocket(item.data)"
                            >
                                <i class="icon2017 icon-delete_01"></i>
                            </div>
                        </template>
                        <template v-else>
                            <div
                                class="listing-table-action_cell"
                                v-tip="{
                                    content: $t('listingMgmt.listingTable.tip5'),
                                    placement: 'bottom',
                                    delay: 500,
                                    maxWidth: '320px'
                                }"
                                @click="sendLeads(item.data)"
                            >
                                <i class="icon2017 icon-send_01"></i>
                            </div>

                            <div
                                v-if="canEditDeleteUK(item.data)"
                                class="listing-table-action_cell"
                                v-tip="{
                                    content: isShowPostTip ? getShareTip(item.data) : '',
                                    placement: 'bottom',
                                    delay: 500,
                                    maxWidth: '320px'
                                }"
                                @click="sharePost(item.data)"
                            >
                                <i class="icon2017 icon-share"></i>
                            </div>

                            <!-- Auxiliary text code hidden in more drop-downs to do lazy loading ====== start -->
                            <div
                                v-if="canEditDeleteUK(item.data) && isShowTextCode"
                                v-lazy="{
                                    load: loadTextCode,
                                    loadParam: item.data
                                }"
                            ></div>
                            <!-- Auxiliary text code hidden in more drop-downs to do lazy loading ====== start -->

                            <DropDown
                                v-if="
                                    canEditDeleteUK(item.data) &&
                                    (isShowTextCode ||
                                        isShowAds ||
                                        showCMA ||
                                        item.data.pocketListing ||
                                        item.data.mlsFeedListing ||
                                        isShowBuyerCompensation ||
                                        isUK)
                                "
                                class="listing-table-action_more"
                                v-tip="{
                                    content: $st('common', 'listingMgmt.moreAction'),
                                    placement: 'bottom',
                                    delay: 500,
                                    maxWidth: '320px'
                                }"
                                :isOpen.sync="item.data.isOpenAction"
                            >
                                <template #body>
                                    <div class="listing-table-action_cell">
                                        <i class="icon2017 icon-more_01"></i>
                                    </div>
                                </template>

                                <template #dropdown>
                                    <div
                                        class="listing-table-action-dropdown"
                                        @click="item.data.isOpenAction = false"
                                    >
                                        <div
                                            v-if="isShowTextCode"
                                            class="listing-table-action-dropdown_cell"
                                            :key="'tc_' + item.data.id"
                                            @click="openTextCode(item.data, $event)"
                                        >
                                            <i class="icon2017 icon-textcode_01"></i>

                                            <span>{{
                                                $t("listingMgmt.listingTable.label.setTextCode")
                                            }}</span>
                                        </div>

                                        <div
                                            v-if="isShowAds"
                                            class="listing-table-action-dropdown_cell"
                                            @click="goAddLP(item.data)"
                                        >
                                            <i class="icon2017 icon-ad_01"></i>
                                            <span>{{
                                                $t("listingMgmt.listingTable.label.createAd")
                                            }}</span>
                                        </div>
                                        <div
                                            v-if="!isUK && showCMA"
                                            class="listing-table-action-dropdown_cell"
                                            @click="goCMA(item.data, CMA_LEVEL.Basic)"
                                        >
                                            <i class="icon2017 icon-cma"></i>
                                            <span>{{ $t("cma.createBasicCMA") }}</span>
                                        </div>
                                        <div
                                            v-if="!isUK && showCMA"
                                            class="listing-table-action-dropdown_cell"
                                            @click="goCMA(item.data)"
                                        >
                                            <i class="icon2017 icon-presentation2"></i>
                                            <span>{{ $t("cma.createListingPresentation") }}</span>
                                        </div>
                                        <div
                                            class="listing-table-action-dropdown_cell"
                                            v-if="
                                                page[flag].showingConfig &&
                                                page[flag].showingConfig[item.data.id] != 1 &&
                                                !hideShowing(item.data)
                                            "
                                            @click="bookShowing(item.data)"
                                        >
                                            <i class="icon2017 icon-CRM-showing"></i>
                                            <span>{{
                                                $t("listingMgmt.listingTable.label.bookShowing")
                                            }}</span>
                                        </div>
                                        <div
                                            v-if="!isUK && isShowBuyerCompensation"
                                            class="listing-table-action-dropdown_cell"
                                            @click="showBuyerPop(item.data)"
                                        >
                                            <i class="icon2017 icon-compensation_01"></i>
                                            <span>{{
                                                !!item.data.buyerAmount
                                                    ? $t(
                                                          "listingMgmt.listingTable.label.editBuyerCompensation"
                                                      )
                                                    : $t(
                                                          "listingMgmt.listingTable.label.addBuyerCompensation"
                                                      )
                                            }}</span>
                                        </div>
                                        <div
                                            class="listing-table-action-dropdown_cell"
                                            :class="{
                                                disabled: !canEditPocket(item.data)
                                            }"
                                            @click="editPocket(item.data)"
                                            v-tip="{
                                                content: canEditPocket(item.data)
                                                    ? ''
                                                    : localizeListing(
                                                          $t('listingMgmt.listingTable.editTip')
                                                      ),
                                                placement: 'top',
                                                delay: 500,
                                                maxWidth: '320px'
                                            }"
                                        >
                                            <i class="icon2017 icon-edit_01"></i>

                                            <span>{{
                                                $t("listingMgmt.listingTable.label.edit")
                                            }}</span>
                                        </div>

                                        <div
                                            class="listing-table-action-dropdown_cell"
                                            :class="{
                                                disabled:
                                                    !showOrCanDeletePocketEntry(item.data) &&
                                                    !canEditDeleteUK(item.data)
                                            }"
                                            @click="deletePocket(item.data)"
                                            v-tip="{
                                                content:
                                                    showOrCanDeletePocketEntry(item.data) ||
                                                    canEditDeleteUK(item.data)
                                                        ? ''
                                                        : localizeListing(
                                                              $t(
                                                                  'listingMgmt.listingTable.deleteTip'
                                                              )
                                                          ),
                                                placement: 'top',
                                                delay: 500,
                                                maxWidth: '320px'
                                            }"
                                        >
                                            <i class="icon2017 icon-delete_01"></i>

                                            <span>{{
                                                $t("listingMgmt.listingTable.label.delete")
                                            }}</span>
                                        </div>
                                    </div>
                                </template>
                            </DropDown>
                        </template>
                    </div>
                </template>
            </DataTableColumn>
        </template>
    </DataTable>
</template>
<script>
import { basePermission, crmUtils, htmlString, infoData, authority } from "crm";
import { components, utils, formats } from "common";
import { useIsUK } from "@/hooks";
import { BS, CMA_LEVEL } from "@/js/cmas/common/const.js";
import SocialPop from "@/js/campaigns/SocialMedia/socialPop.js";
import SocialAuthority from "@/js/campaigns/SocialMedia/socialAuthority";
import bookShowingPop from "@/showingTool/index.js";
import ListingAgent from "@/js/common-module/ListingAgent/index.vue";
import { createQuickCMA } from "@/js/cmas/common/quickCMA";
import MixinBuyerCompensation from "@/js/listing-detail/buyerCompensation/index.js";
const { getFacebookSvg, getTwitterSvg, getLinkedinSvg } = htmlString;
const { CheckBox, DataTable, DropDown } = components;

export default {
    name: "LDTableView",
    mixins: [MixinBuyerCompensation],
    props: {
        page: {
            type: Object,
            default: () => ({})
        },
        flag: {
            type: String,
            default: ""
        },
        isAgentList: {
            type: Boolean,
            default: false
        },
        isShowTableNav: {
            type: Boolean,
            default: false
        },
        isFullHosting: {},
        forwardReceiveFlag: {},
        hasVirtualNumber: {},
        numberMigratingTip: {},
        loadMatchLead: {},
        useDialer: {},
        hasListingPromotion: {},
        showFillItOut: {},
        loadTextCode: {},
        bindEmail: {},
        tableData: {
            type: Array,
            default: () => []
        },
        tableSort: {
            type: Object,
            default: () => ({})
        },
        isSelectAll: {
            type: Boolean,
            default: false
        },
        infinityLoadStatus: {
            type: [String, Array, Object, Number]
        },
        infinityLoad: {
            type: Function,
            default: () => {}
        },
        actionSortConfig: {
            type: Object,
            default: () => ({})
        },
        selectAllBeforeChange: {
            type: Function,
            default: () => {}
        },
        selectBeforeChange: {
            type: Function,
            default: () => {}
        },
        isSHowOpenHouseColumn: {
            type: Boolean,
            default: true
        },
        getOpenHouseScheduleTip: {
            type: Function,
            default: () => {}
        },
        isExpiredListingType: {
            type: Boolean,
            default: false
        },
        isHoldListingTab: {
            type: Boolean,
            default: false
        },
        actionStatus: {
            type: Object,
            default: () => ({})
        },
        isShowBuyerCompensation: {
            type: Boolean,
            default: false
        },
        targetBuyerLabel: {
            type: String,
            default: ""
        },
        isTeamListingForClosed: {
            type: Boolean,
            default: false
        },
        targetSource: {
            type: String,
            default: ""
        }
    },
    components: { CheckBox, DataTable, DropDown, ListingAgent },
    data() {
        return {
            isShowSocialEntry: false,
            CMA_LEVEL
        };
    },
    setup() {
        const isUK = useIsUK();
        return {
            isUK
        };
    },
    methods: {
        localizeListing(label) {
            if (this.isUK) {
                return utils.localize.listingToUK(label);
            }
            return label;
        },
        canEditDeleteUK(info) {
            if (!info || !this.isUK) {
                return false;
            }
            const curLoggedInUserId = +infoData.getUserInfo()?.userId;
            const { primaryAgentId, agentId1, agentId2, agentId3, createUser } = info;
            const agents = [primaryAgentId, agentId1, agentId2, agentId3]
                .filter(Boolean)
                .map((item) => +item);
            const isCreator = +createUser === +curLoggedInUserId;
            const isAgent = agents.includes(curLoggedInUserId);

            const hasPropertyAuth = authority.checkUserRight("MANAGE_COMPANY_PROPERTY");

            return isAgent || isCreator || hasPropertyAuth;
        },
        onMarketDaysValue(data) {
            const res = this.isValid(data.daysOnList)
                ? this.$t("listingMgmt.listingTable.onMarketDay", {
                      count: data.daysOnList,
                      countStr: this.$ft.number(data.daysOnList)
                  })
                : this.$t("listingMgmt.listingTable.onMarketDay", {
                      count: 0,
                      countStr: 0
                  });
            return res;
        },
        getHouseArea(data) {
            if (this.isUK) {
                return data.totalBuildingSqft ?? "--";
            }
            return this.isValid(data.sqft) ? this.$ft.zero_to(this.$ft.number(data.sqft)) : "--";
        },
        getDetailUrl(urlPath) {
            if (!urlPath) {
                return "";
            }
            urlPath += (urlPath.indexOf("?") >= 0 ? "&" : "?") + "isPopup=0";
            let domain = this.targetSource || infoData.userInfo.siteUrl;
            if (!domain) {
                return "";
            }
            return domain + urlPath;
        },
        showBuyerPop(listing) {
            this.createBuyerPop(
                {
                    listing,
                    amount: listing.buyerAmount,
                    label: this.targetBuyerLabel
                },
                (data) => {
                    this.$emit("updateTargetBuyerCompInfo", data);
                }
            );
        },
        bookShowing(listing) {
            bookShowingPop({
                listingId: listing.id,
                actionStatus: this.actionStatus,
                trackGaClass: "listingshowing-agent-card"
            });
        },
        hideShowing(listing) {
            if (this.isUK) {
                return listing.listingStatus !== "Available";
            }

            const statusCondition = [
                "pending",
                "under contract",
                "sold",
                "under active contract",
                "contingent",
                "active under contract"
            ].includes(listing.listingStatus.toLowerCase());
            return statusCondition;
        },
        formatAddress(data) {
            if (this.isUK) {
                return data.address;
            }
            let res = "";
            let stateAndZipCode = [];

            if (data.city) {
                res = `${data.city}`;
            }
            if (data.state) {
                stateAndZipCode.push(data.state);
            }
            if (data.zipCode) {
                stateAndZipCode.push(data.zipCode);
            }
            res = res ? `${res}, ${stateAndZipCode.join(" ")}` : `${stateAndZipCode.join(" ")}`;

            return res;
        },
        showListingDetail({ id, detailLink, multiFieldsJson } = {}) {
            if (
                this.isTeamListingForClosed &&
                multiFieldsJson?.chimePermissions === "Participant Only Listing"
            ) {
                return false;
            }
            if (this.isTeamListingForClosed) {
                const detailUrl = this.getDetailUrl(detailLink);
                detailUrl && window.open(detailUrl, "_blank");
            } else {
                id &&
                    this.$emit("showDetailPop", {
                        id,
                        isExpiredListingType: this.isExpiredListingType
                    });
                this.$emit("closeFilter");
            }
        },
        isValid(e) {
            return !(isNaN(e) || e === null || e === undefined || e == -1 || e === "");
        },
        zero_to(value, type) {
            if (!this.isValid(value)) {
                return "--";
            }

            return value || type;
        },
        formatBedBath(bedrooms, bathrooms) {
            let realBedrooms = this.zero_to(formats.number(bedrooms));
            let realBathrooms = this.zero_to(
                this.isValid(bathrooms) ? parseFloat(bathrooms).toFixed(2) : bathrooms
            );
            if (this.isUK) {
                realBathrooms = realBathrooms.toString().replace(/\.0+$/, "");
            }
            let bedroomsLabel = this.isValid(bedrooms)
                ? realBedrooms
                : `<span>${realBedrooms}</span>`;
            let bathroomsLabel = this.isValid(bedrooms)
                ? realBathrooms
                : `<span>${realBathrooms}</span>`;

            return `${bedroomsLabel}/${bathroomsLabel}`;
        },
        formatsPrice(value) {
            if (!value || value.toString() === "-1") {
                return "--";
            }
            return this.isUK ? formats.pound(value || 0) : formats.dollar(value || 0);
        },
        formatListingStatus(listingStatus) {
            const classMap = {
                Active: "Active",
                Available: "Active",
                Pending: "Pending",
                Sold: "Sold",
                Contingent: "Contingent"
            };

            return classMap[listingStatus] || "Normal";
        },
        goCMA(data, level) {
            let { leadId = "", leadName = "" } = this.$route.query || {};
            createQuickCMA({
                vm: this,
                lead: { leadId: leadId, leadname: leadName },
                listingInfo: data,
                type: BS.buyer,
                level
            });
        },
        setSort(data) {
            this.$emit("setSort", data);
        },
        selectAllChange(data) {
            this.$emit("selectAllChange", data);
        },
        selectChange(event, data) {
            this.$emit("selectChange", event, data);
        },
        moreMLSInfo(data, type) {
            this.$emit("moreMLSInfo", data, type);
        },
        openDialog(data) {
            if (data.leadCount > 0) {
                this.$emit("openDialog", data);
            }
        },
        sendLeads(data) {
            this.$emit("sendLeads", data);
        },
        openTextCode(data, event) {
            this.$emit("openTextCode", data, event);
        },
        goAddLP(data) {
            this.$emit("goAddLP", data);
        },
        getShareTip(data) {
            const detailUrl = (data.detailUrl || "").replace(/[?&]isPopup=0/, "");
            return `<div class="list-share-tip">
                        <p class="title">Post to Social Media</p>
                        <p class="content">
                            <a target="_blank" href="https://www.facebook.com/share.php?u=${detailUrl}" rel="nofollow" ga-click="listingmgmt_facebook">
                                ${getFacebookSvg()}
                            </a>
                            <a target="_blank" class="ml-10" href='https://twitter.com/share?text=Instantly view all photos and details of this hot listing and own the perfect place before other buyers.&url=${detailUrl}' rel="nofollow" ga-click="listingmgmt_twitter">
                                ${getTwitterSvg()}
                            </a>
                            <a target="_blank" class="ml-10" href="https://www.linkedin.com/shareArticle?url=${detailUrl}" rel="nofollow" ga-click="listingmgmt_linkedin">
                                ${getLinkedinSvg()}
                            </a>
                        </p>
                    </div>`;
        },
        editPocket(data, event) {
            if (!this.canEditPocket(data)) {
                return false;
            }
            this.$emit("editPocket", data, event);
        },
        deletePocket(data, event) {
            if (!this.showOrCanDeletePocketEntry(data)) {
                return false;
            }
            this.$emit("deletePocket", data, event);
        },
        dealUpdateTime(data) {
            let d = data.lastPrimaryChangeTime;
            if (utils.isString(d) && d.indexOf("UTC") === -1) {
                //  Determine whether it is a Firefox browser
                /* eslint-disable */
                if (navigator.userAgent.indexOf("Firefox") > -1) {
                    d = new Date(d + "Z").getTime();
                } else if (navigator.userAgent.indexOf("Safari") > -1) {
                    d = new Date(d.replace(/-/g, "/") + " UTC").getTime();
                } else {
                    d = new Date(d + " UTC").getTime();
                }
            }
            /* eslint-disable */
            return Vue.filter("recent_time")(d);
        },
        showMLSbox(data) {
            this.$emit("showMLSbox", data);
        },
        async getSocialRightInfo() {
            this.isShowSocialEntry = await SocialAuthority.showSocialEntry();
        },
        sharePost(data) {
            if (this.isShowPostTip) {
                return;
            }
            this.handleAddPost(data);
        },
        handleAddPost(data) {
            SocialPop.createListingPostPop({ source: "Listing Discovery" }, data);
        },
        showEditOptions(listing) {
            const { pocketListing } = listing || {};
            if (this.isUK) {
                return this.canEditDeleteUK(listing);
            }
            if (!this.isRLSPermission) {
                return pocketListing;
            } else {
                const { isAdminInRLS, isCreator, curListingStatus, isInSameGroup } =
                    this.getCommonInfo(listing);
                if (
                    ["active", "pending", "hold", "sold", "coming soon"].includes(curListingStatus)
                ) {
                    return (isAdminInRLS && isInSameGroup) || isCreator;
                } else {
                    return false;
                }
            }
        },
        canEditPocket(listing) {
            if (this.isUK) {
                return this.canEditDeleteUK(listing);
            }
            const { pocketListing, rlsPublishStatus } = listing || {};
            if (!this.isRLSPermission) {
                return pocketListing;
            } else {
                const { isAdminInRLS, isCreator, curListingStatus, isInSameGroup } =
                    this.getCommonInfo(listing);
                if (["active", "pending", "hold", "coming soon"].includes(curListingStatus)) {
                    return (isAdminInRLS && isInSameGroup) || isCreator;
                } else if (["sold"].includes(curListingStatus)) {
                    return (
                        (isAdminInRLS && (isInSameGroup || isCreator)) ||
                        (isCreator && rlsPublishStatus === 0)
                    );
                } else {
                    return false;
                }
            }
        },
        showOrCanDeletePocketEntry(listing) {
            if (this.isUK) {
                return this.canEditDeleteUK(listing);
            }
            const { pocketListing, rlsPublishStatus } = listing || {};
            if (!this.isRLSPermission) {
                return pocketListing;
            } else {
                const { isAdminInRLS, isCreator, curListingStatus, isInSameGroup } =
                    this.getCommonInfo(listing);
                if (
                    ["active", "pending", "hold", "sold", "coming soon"].includes(curListingStatus)
                ) {
                    return (
                        (isAdminInRLS && (isInSameGroup || isCreator)) ||
                        (isCreator && rlsPublishStatus === 0)
                    );
                } else {
                    return false;
                }
            }
        },
        getCommonInfo(listing) {
            const { listingStatus, multiFieldsJson } = listing || {};
            const { userId, isAdminInRLS } = infoData.getUserInfo();
            const chimeCreateId = multiFieldsJson?.chimeCreateId ?? "";
            const isCreator = userId == chimeCreateId || this.isUK;
            const curListingStatus = (listingStatus || "").toLocaleLowerCase();
            const sameGroupInfo = this.page[this.flag].sameGroupInfo || {};
            const isInSameGroup = sameGroupInfo[chimeCreateId];
            return {
                isAdminInRLS,
                isCreator,
                curListingStatus,
                isInSameGroup
            };
        }
    },
    async mounted() {
        window.handleAddPost = this.handleAddPost;
        await crmUtils.awaitWrap(this.getSocialRightInfo());
    },
    computed: {
        isShowTextCode({ hasVirtualNumber }) {
            const { Dialer } = basePermission;

            return !!(Dialer && hasVirtualNumber);
        },
        isShowAds({ hasListingPromotion }) {
            const { ListingAds } = basePermission;

            return !!(ListingAds && hasListingPromotion);
        },
        isShowPostTip() {
            return !this.isShowSocialEntry;
        },
        showCMA({ isExpiredListingType }) {
            return !this.isUK && !isExpiredListingType && basePermission.CMA;
        },
        isRLSPermission() {
            const { rls } = basePermission;
            return !!rls;
        }
    },
    beforeDestroy() {
        delete window.handleAddPost;
    }
};
</script>
<style lang="less">
@import url("./index.less");
</style>
