module.exports = {
    testEnvironment: "jsdom",
    roots: ["<rootDir>/src", "<rootDir>/test"],
    testMatch: ["**/test/**/*.(test|spec).js", "**/?(*.)+(spec|test).js"],
    moduleFileExtensions: ["js", "json", "vue", "ts", "tsx"],

    // 限制并发数
    maxWorkers: 2,

    // 启用缓存
    cache: true,
    cacheDirectory: "<rootDir>/.jest-cache",

    // 清理模块缓存
    clearMocks: true,
    resetMocks: true,
    restoreMocks: true,

    transform: {
        "^.+\\.vue$": "@vue/vue2-jest",
        "^.+\\.(js|jsx|ts|tsx)$": "babel-jest"
    },

    transformIgnorePatterns: ["node_modules/(?!(vue-router|@vue/test-utils)/)"],

    moduleNameMapper: {
        "^@/(.*)$": "<rootDir>/src/$1",
        "^root/(.*)$": "<rootDir>/../$1",
        "^common-module/(.*)$": "<rootDir>/src/js/common-module/$1",
        "^common$": "<rootDir>/../crm-packages/src/index.js",
        "^crm$": "<rootDir>/../crm-common/index.js",
        "^fabric$": "<rootDir>/__mock__/fabric.js",
        "^vuex$": "vuex",
        "^vue$": "vue",
        "\\.(css|less|scss|sass)$": "<rootDir>/__mock__/styleMock.js"
    },

    setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
    collectCoverage: false,
    coverageReporters: ["text", "lcov", "html"],
    coverageDirectory: "<rootDir>/coverage",
    collectCoverageFrom: [
        "src/**/*.{js,vue}",
        "!src/**/lib/**",
        "!src/**/plugins/**",
        "!src/**/*.min.js"
    ],
    verbose: false, // 关闭详细输出减少内存使用

    globals: {
        "vue-jest": {
            babelConfig: true,
            hideStyleWarn: true,
            experimentalCSSCompile: true
        }
    },

    // 添加内存和性能相关配置
    testTimeout: 30000,

    // 限制测试文件大小
    maxConcurrency: 5
};
