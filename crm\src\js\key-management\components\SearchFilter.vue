<template>
    <div class="search-filter" data-testid="search-filter-container">
        <div class="filter-left">
            <div class="search-input-wrapper">
                <i class="icon2017 icon-search_01"></i>
                <input
                    type="text"
                    class="search-input"
                    :placeholder="$t('search.placeholder')"
                    v-model="searchKeyword"
                    @input="onSearchChange"
                    data-testid="search-filter-input"
                />
            </div>
        </div>
        <div class="filter-right">
            <div class="filter-item">
                <div class="filter-dropdown" @click="toggleDropdown('status')" data-testid="search-filter-status-dropdown">
                    <span class="filter-label">{{ $t('filters.status.label') }}</span>
                    <span class="filter-value">{{ statusFilter.label }}</span>
                    <i class="icon2017 icon-arrow_01_down"></i>
                </div>
                <div v-if="dropdowns.status" class="dropdown-menu">
                    <div
                        v-for="option in statusOptions"
                        :key="option.value"
                        class="dropdown-item"
                        @click="selectFilter('status', option)"
                        data-testid="search-filter-status-option"
                    >
                        {{ option.label }}
                    </div>
                </div>
            </div>
            
            <div class="filter-item">
                <div class="filter-dropdown" @click="toggleDropdown('holder')" data-testid="search-filter-holder-dropdown">
                    <span class="filter-label">{{ $t('filters.holder.label') }}</span>
                    <span class="filter-value">{{ holderFilter.label }}</span>
                    <i class="icon2017 icon-arrow_01_down"></i>
                </div>
                <div v-if="dropdowns.holder" class="dropdown-menu">
                    <div
                        v-for="option in holderOptions"
                        :key="option.value"
                        class="dropdown-item"
                        @click="selectFilter('holder', option)"
                        data-testid="search-filter-holder-option"
                    >
                        {{ option.label }}
                    </div>
                </div>
            </div>
            
            <div class="filter-item">
                <div class="filter-dropdown" @click="toggleDropdown('propertyType')" data-testid="search-filter-property-type-dropdown">
                    <span class="filter-label">{{ $t('filters.property_type.label') }}</span>
                    <span class="filter-value">{{ propertyTypeFilter.label }}</span>
                    <i class="icon2017 icon-arrow_01_down"></i>
                </div>
                <div v-if="dropdowns.propertyType" class="dropdown-menu">
                    <div
                        v-for="option in propertyTypeOptions"
                        :key="option.value"
                        class="dropdown-item"
                        @click="selectFilter('propertyType', option)"
                        data-testid="search-filter-property-type-option"
                    >
                        {{ option.label }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SearchFilter',
    langModule: 'key-management',
    data() {
        return {
            searchKeyword: '',
            dropdowns: {
                status: false,
                holder: false,
                propertyType: false
            },
            statusFilter: { value: 'all', label: '' },
            holderFilter: { value: 'all', label: '' },
            propertyTypeFilter: { value: 'all', label: '' }
        };
    },
    computed: {
        statusOptions() {
            return [
                { value: 'all', label: this.$t('filters.status.all') },
                { value: 'available', label: this.$t('filters.status.available') },
                { value: 'checked_out', label: this.$t('filters.status.checked_out') },
                { value: 'lost', label: this.$t('filters.status.lost') },
                { value: 'damaged', label: this.$t('filters.status.damaged') },
                { value: 'archived', label: this.$t('filters.status.archived') }
            ];
        },
        holderOptions() {
            return [
                { value: 'all', label: this.$t('filters.holder.all') }
                // Holder options will be dynamically loaded via API
            ];
        },
        propertyTypeOptions() {
            return [
                { value: 'all', label: this.$t('filters.property_type.all') },
                { value: 'for_sale', label: this.$t('filters.property_type.for_sale') },
                { value: 'to_let', label: this.$t('filters.property_type.to_let') }
            ];
        }
    },
    watch: {
        // Watch for route changes
        '$route'() {
            this.setPropertyTypeFromRoute();
        }
    },
    methods: {
        onSearchChange() {
            this.$emit('search-change', this.searchKeyword);
        },
        toggleDropdown(type) {
            // Close other dropdowns
            Object.keys(this.dropdowns).forEach(key => {
                if (key !== type) {
                    this.dropdowns[key] = false;
                }
            });
            // Toggle current dropdown
            this.dropdowns[type] = !this.dropdowns[type];
        },
        selectFilter(type, option) {
            if (type === 'status') {
                this.statusFilter = option;
            } else if (type === 'holder') {
                this.holderFilter = option;
            } else if (type === 'propertyType') {
                this.propertyTypeFilter = option;
            }

            this.dropdowns[type] = false;
            this.$emit('filter-change', {
                status: this.statusFilter.value,
                holder_id: this.holderFilter.value === 'all' ? '' : this.holderFilter.value,
                property_type: this.propertyTypeFilter.value
            });
        },
        /**
         * Set property type filter based on route parameter
         */
        setPropertyTypeFromRoute() {
            const routeType = this.$route.params.type;

            if (routeType === 'sales') {
                // For sales route, set to 'for_sale'
                const forSaleOption = this.propertyTypeOptions.find(option => option.value === 'for_sale');
                if (forSaleOption) {
                    this.propertyTypeFilter = forSaleOption;
                    this.emitFilterChange();
                }
            } else if (routeType === 'lettings') {
                // For lettings route, set to 'to_let'
                const toLetOption = this.propertyTypeOptions.find(option => option.value === 'to_let');
                if (toLetOption) {
                    this.propertyTypeFilter = toLetOption;
                    this.emitFilterChange();
                }
            }
        },
        /**
         * Emit filter change event
         */
        emitFilterChange() {
            this.$emit('filter-change', {
                status: this.statusFilter.value,
                holder_id: this.holderFilter.value === 'all' ? '' : this.holderFilter.value,
                property_type: this.propertyTypeFilter.value
            });
        },

        /**
         * Initialize filter labels with default values
         */
        initializeFilterLabels() {
            this.statusFilter.label = this.$t('filters.status.all');
            this.holderFilter.label = this.$t('filters.holder.all');
            this.propertyTypeFilter.label = this.$t('filters.property_type.all');
        }
    },
    mounted() {
        // Initialize filter labels
        this.initializeFilterLabels();

        // Set initial property type based on route
        this.setPropertyTypeFromRoute();

        // Click outside to close dropdown
        document.addEventListener('click', (e) => {
            if (!this.$el.contains(e.target)) {
                Object.keys(this.dropdowns).forEach(key => {
                    this.dropdowns[key] = false;
                });
            }
        });
    }
};
</script>

<style scoped>
.search-filter {
    display: flex;
    align-items: stretch;
    gap: 10px;
    padding: 10px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px 6px 0px 0px;
}

.filter-left {
    flex: 1;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 300px;
    height: 30px;
    padding: 0 10px;
    background: #FFFFFF;
    border: 1px solid #C6C8D1;
    border-radius: 6px;
}

.search-input-wrapper .icon2017 {
    width: 14px;
    height: 14px;
    color: #C6C8D1;
    margin-right: 10px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}

.search-input::placeholder {
    color: #C6C8D1;
}

.filter-right {
    display: flex;
    gap: 10px;
}

.filter-item {
    position: relative;
}

.filter-dropdown {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0);
    border-radius: 6px;
    cursor: pointer;
    white-space: nowrap;
}

.filter-dropdown:hover {
    background: #f5f5f5;
}

.filter-label,
.filter-value {
    font-family: 'SF Pro';
    font-weight: 510;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}

.filter-dropdown .icon2017 {
    width: 12px;
    height: 12px;
    color: #A0A3AF;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 150px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.dropdown-item {
    padding: 8px 12px;
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
    cursor: pointer;
}

.dropdown-item:hover {
    background: #f5f5f5;
}

.dropdown-item:first-child {
    border-radius: 6px 6px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 6px 6px;
}

@media (max-width: 768px) {
    .search-filter {
        overflow-x: auto;
        padding-bottom: 10px; /* Add some padding for the scrollbar */
    }
    .filter-right {
       flex-wrap: nowrap;
    }
    .filter-item {
        flex-shrink: 0; /* Prevent items from shrinking */
    }
     .search-input-wrapper {
        min-width: 250px; /* Ensure search input has a minimum width */
    }
}
</style>
