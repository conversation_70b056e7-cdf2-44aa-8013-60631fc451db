import { crmUtils } from 'crm';

import * as mockApi from './mock';
const useMock = true; // Set to false to use real API

/**
 * Key Management API Service
 * API interfaces for key management functionality
 */
export default {
    /**
     * Get key list
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number, default 1
     * @param {number} params.limit - Items per page, default 20, max 100
     * @param {string} params.status - Key status filter: available, checked_out, lost, damaged, archived
     * @param {string} params.holder_id - Key holder ID
     * @param {string} params.property_id - Property ID for querying keys, used in property detail page
     * @param {string} params.property_type - Property type filter: for_sale, to_let
     * @param {string} params.search - Search keyword (key holder name, property address)
     * @returns {Promise} API response
     */
    getKeyList(params = {}) {
        if (useMock) {
            return mockApi.getKeys(params);
        }
        // Set default parameters
        const defaultParams = {
            page: 1,
            limit: 20
        };

        const queryParams = { ...defaultParams, ...params };

        return crmUtils.sendAjax({
            url: "/api/keys",
            type: "GET",
            data: queryParams
        }).then(response => {
            // Ensure returned data structure meets expectations
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data.data || [],
                    total: response.data.total || 0,
                    page: queryParams.page,
                    limit: queryParams.limit
                };
            }
            return {
                success: false,
                data: [],
                total: 0,
                page: queryParams.page,
                limit: queryParams.limit
            };
        }).catch(error => {
            console.error('Failed to get key list:', error);
            return {
                success: false,
                data: [],
                total: 0,
                page: queryParams.page,
                limit: queryParams.limit,
                error: error.message || 'Failed to get key list'
            };
        });
    },

    /**
     * Get key detail
     * @param {string} keyId - Key ID
     * @returns {Promise} API response
     */
    async getKeyDetail(keyId) {
        if (useMock) {
            const response = await mockApi.getKeys({id: keyId});
            const key = response.data.find(k => k.id === keyId)
            return {
                success: !!key,
                data: key || {}
            }
        }
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}`,
            type: "GET"
        }).then(response => {
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data || {}
                };
            }
            return {
                success: false,
                data: {}
            };
        }).catch(error => {
            console.error('Failed to get key detail:', error);
            return {
                success: false,
                data: {},
                error: error.message || 'Failed to get key detail'
            };
        });
    },

    /**
     * Get key statistics
     * @param {Object} params - Query parameters
     * @param {string} params.status - Key status filter: available, checked_out, lost, damaged, archived
     * @param {string} params.holder_id - Key holder ID
     * @param {string} params.property_id - Property ID
     * @param {string} params.property_type - Property type filter: for_sale, to_let
     * @param {string} params.search - Search keyword (key holder name, property address)
     * @returns {Promise} API response
     */
    getKeyStatistics(params = {}) {
        if (useMock) {
            return mockApi.getKeyStatistics(params);
        }
        return crmUtils.sendAjax({
            url: "/api/keys/statistics",
            type: "GET",
            data: params
        }).then(response => {
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data || {}
                };
            }
            return {
                success: false,
                data: {}
            };
        }).catch(error => {
            console.error('Failed to get key statistics:', error);
            return {
                success: false,
                data: {},
                error: error.message || 'Failed to get key statistics'
            };
        });
    },

    /**
     * Create a new key
     * @param {Object} keyData - The data for the new key
     * @returns {Promise} API response
     */
    createKey(keyData) {
        if (useMock) {
            return mockApi.createKey(keyData);
        }
        return crmUtils.sendAjax({
            url: "/api/keys",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(keyData)
        });
    },

    /**
     * Update key information
     * @param {string} keyId - Key ID
     * @param {Object} updateData - Update data
     * @returns {Promise} API response
     */
    updateKey(keyId, updateData) {
        if (useMock) {
            return mockApi.updateKey(keyId, updateData);
        }
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}`,
            type: "PUT",
            contentType: "application/json",
            data: JSON.stringify(updateData)
        }).then(response => {
            return {
                success: response.status?.code === 200,
                data: response.data || {}
            };
        }).catch(error => {
            console.error('Failed to update key:', error);
            return {
                success: false,
                error: error.message || 'Failed to update key'
            };
        });
    },

    /**
     * Perform an operation on a key
     * @param {string} keyId - Key ID
     * @param {Object} operationData - Operation data
     * @returns {Promise} API response
     */
    operateKey(keyId, operationData) {
        if (useMock) {
            return mockApi.operateKey(keyId, operationData);
        }
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}/operations`,
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(operationData)
        });
    },

    /**
     * Get next key code
     * @param {Object} params - Query parameters {propertyId: string}
     * @returns {Promise} API response
     */
    getNextKeycode(params) {
        if (useMock) {
            return mockApi.getNextKeycode(params);
        }
        return crmUtils.sendAjax({
            url: "/api/keys/next-key-code",
            type: "GET",
            data: params
        });
    },

    /**
     * Get key activity logs
     * @param {string} keyId - Key ID
     * @param {Object} params - Query parameters
     * @returns {Promise} API response
     */
    getKeyActivities(keyId, params) {
        if (useMock) {
            return mockApi.getKeyActivities(keyId, params);
        }
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}/activities`,
            type: "GET",
            data: params
        });
    }
};
