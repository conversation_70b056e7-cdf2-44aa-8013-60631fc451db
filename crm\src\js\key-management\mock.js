
import { v4 as uuidv4 } from 'uuid';

// 模拟钥匙数据库表
let mockKeys = Array.from({ length: 50 }, (_, i) => {
    const propertyId = `prop-uuid-${123 + i}`;
    const status_options = ['available', 'checked_out', 'lost', 'damaged', 'archived'];
    const property_type_options = ['for_sale', 'to_let'];
    const holder_type_options = ['internal', 'external'];
    const status = status_options[i % status_options.length];
    let holder_name = null;
    let holder_id = null;
    if(status === 'checked_out'){
        holder_name = `User ${i}`;
        holder_id = `user-uuid-${i}`;
    }

    return {
    id: `key-uuid-${123 + i}`,
    key_code: `KEY-${propertyId}-${String(i + 1).padStart(3, '0')}`,
    property_id: propertyId,
    property_address: `${123 + i} Main Street, London SW1A 1AA`,
    property_type: property_type_options[i % property_type_options.length],
    description: `Description for key ${i + 1}.`,
    photo_url: i % 3 === 0 ? `https://picsum.photos/200/300?random=${i}` : null,
    status: status,
    storage_location: 'Reception Desk',
    current_holder_id: holder_id,
    current_holder_name: holder_name,
    current_holder_type: holder_type_options[i % holder_type_options.length],
    total_keys: i % 5 === 0 ? 2 : 1,
    metadata: { expected_return_date: '2025-08-01' },
    team_id: 101,
    created_at: new Date(Date.now() - (50-i) * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - (20-i) * 24 * 60 * 60 * 1000).toISOString(),
    last_activity_at: new Date(Date.now() - (20-i) * 12 * 60 * 60 * 1000).toISOString(),
    };
});

// 模拟钥匙活动日志表
let mockKeyActivities = mockKeys.flatMap(key => {
    const activity_types = ['create', 'checkout', 'checkin', 'report_lost', 'report_damaged', 'edit', 'archive', 'unarchive'];
    return Array.from({length: Math.floor(Math.random() * 5) + 1}, (_, i) => ({
        id: `activity-uuid-${key.id}-${i}`,
        key_id: key.id,
        property_id: key.property_id,
        activity_type: activity_types[i % activity_types.length],
        performed_by_id: `user-uuid-${789 + i}`,
        performed_by_name: `Admin ${i+1}`,
        target_user_type: 'internal',
        target_user_id: `user-uuid-${123 + i}`,
        target_user_name: `John Doe ${i}`,
        operation_reason: 'Routine operation',
        notes: `Note for activity ${i}`,
        metadata: {},
        created_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
    }));
});

// 模拟延时
const mockApiCall = (data, success = true) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        success,
        data,
        total: data.length || Object.keys(data).length,
      });
    }, 500);
  });
};

// 4.1 获取钥匙列表
export const getKeys = (params = {}) => {
  const { page = 1, limit = 20, status, property_type, search, holder_id, property_id } = params;
  let result = [...mockKeys];

  if (status) {
    result = result.filter(k => k.status === status);
  }
  if (property_type) {
    result = result.filter(k => k.property_type === property_type);
  }
  if (holder_id) {
      result = result.filter(k => k.current_holder_id === holder_id);
  }
  if (property_id) {
    result = result.filter(k => k.property_id === property_id);
  }
  if (search) {
    result = result.filter(k =>
      k.current_holder_name?.toLowerCase().includes(search.toLowerCase()) ||
      k.property_address.toLowerCase().includes(search.toLowerCase())
    );
  }

  const paginatedData = result.slice((page - 1) * limit, page * limit);
  return mockApiCall(paginatedData, true, result.length);
};

// 4.2 创建钥匙
export const createKey = (keyData) => {
  const newKey = {
    id: `key-uuid-${uuidv4()}`,
    status: 'available',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...keyData,
  };
  mockKeys.unshift(newKey);
  return mockApiCall(newKey);
};

// 4.3 编辑钥匙信息
export const updateKey = (id, keyData) => {
  const keyIndex = mockKeys.findIndex(k => k.id === id);
  if (keyIndex === -1) {
    return mockApiCall(null, false);
  }
  mockKeys[keyIndex] = { ...mockKeys[keyIndex], ...keyData, updated_at: new Date().toISOString() };
  return mockApiCall(mockKeys[keyIndex]);
};

// 4.4 钥匙操作接口
export const operateKey = (id, operation) => {
  const { operation_type, data } = operation;
  const keyIndex = mockKeys.findIndex(k => k.id === id);
  if (keyIndex === -1) {
    return mockApiCall(null, false);
  }

  const key = mockKeys[keyIndex];
  const newActivity = {
      id: `activity-uuid-${uuidv4()}`,
      key_id: id,
      property_id: key.property_id,
      activity_type: operation_type,
      performed_by_id: 'user-op-1',
      performed_by_name: 'Current User',
      ...data,
      created_at: new Date().toISOString()
  };

  switch (operation_type) {
    case 'checkout':
        key.status = 'checked_out';
        key.current_holder_id = data.target_user_id;
        key.current_holder_name = data.target_user_name;
        key.current_holder_type = data.target_user_type;
        break;
    case 'checkin':
        key.status = 'available';
        key.current_holder_id = data.current_holder_id;
        key.current_holder_name = data.current_holder_name;
        key.current_holder_type = data.current_holder_type;
        break;
    case 'report_lost':
        key.status = 'lost';
        break;
    case 'report_damaged':
        key.status = 'damaged';
        break;
    case 'archive':
        key.status = 'archived';
        break;
    case 'unarchive':
        key.status = 'available';
        break;
    default:
        return mockApiCall({ message: "Invalid operation type" }, false);
  }

  mockKeyActivities.unshift(newActivity);
  key.updated_at = new Date().toISOString();

  return mockApiCall(key);
};

// 4.5 生成钥匙key_code接口
export const getNextKeycode = (params) => {
    const { propertyId } = params;
    const keysForProperty = mockKeys.filter(k => k.property_id === propertyId);
    const nextKeyCode = `KEY-${propertyId}-${String(keysForProperty.length + 1).padStart(3, '0')}`;
    return mockApiCall({ next_key_code: nextKeyCode });
};

// 4.7 获取钥匙统计信息
export const getKeyStatistics = (params = {}) => {
  let filteredKeys = [...mockKeys];
  const { property_type, search, holder_id, property_id } = params;
    if (property_type) {
        filteredKeys = filteredKeys.filter(k => k.property_type === property_type);
    }
    if (holder_id) {
        filteredKeys = filteredKeys.filter(k => k.current_holder_id === holder_id);
    }
    if (property_id) {
        filteredKeys = filteredKeys.filter(k => k.property_id === property_id);
    }
    if (search) {
        filteredKeys = filteredKeys.filter(k =>
        k.current_holder_name?.toLowerCase().includes(search.toLowerCase()) ||
        k.property_address.toLowerCase().includes(search.toLowerCase())
        );
    }

  const stats = {
    total_keys: filteredKeys.length,
    available: filteredKeys.filter(k => k.status === 'available').length,
    checked_out: filteredKeys.filter(k => k.status === 'checked_out').length,
    lost: filteredKeys.filter(k => k.status === 'lost').length,
    damaged: filteredKeys.filter(k => k.status === 'damaged').length,
    archived: filteredKeys.filter(k => k.status === 'archived').length,
  };
  return mockApiCall(stats);
};

// 4.8 查询key log
export const getKeyActivities = (keyId, params = {}) => {
  const { activity_type, start_date, end_date } = params;
  let activities = mockKeyActivities.filter(a => a.key_id === keyId);

  if (activity_type) {
    activities = activities.filter(a => a.activity_type === activity_type);
  }
  if (start_date) {
    activities = activities.filter(a => new Date(a.created_at) >= new Date(start_date));
  }
  if (end_date) {
    activities = activities.filter(a => new Date(a.created_at) <= new Date(end_date));
  }
  
  return mockApiCall(activities);
};
