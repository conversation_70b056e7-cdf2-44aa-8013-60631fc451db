<template>
    <div class="empty-state" data-testid="empty-state-container">
        <!-- Empty state illustration -->
        <div class="empty-illustration">
            <div class="illustration-container">
                <!-- Background circles -->
                <div class="bg-circle large"></div>
                <div class="bg-circle small"></div>
                
                <!-- Key illustration -->
                <div class="key-illustration">
                    <div class="key-background"></div>
                    <div class="key-body">
                        <div class="key-head"></div>
                        <div class="key-shaft"></div>
                        <div class="key-teeth"></div>
                        <div class="key-teeth-small"></div>
                    </div>
                </div>
                
                <!-- Key tag -->
                <div class="key-tag">
                    <div class="tag-circle"></div>
                    <div class="tag-lines">
                        <div class="tag-line"></div>
                        <div class="tag-line short"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Content -->
        <div class="empty-content">
            <div class="empty-text">
                <h3 class="empty-title" data-testid="empty-state-title">{{ displayTitle }}</h3>
                <p class="empty-description" data-testid="empty-state-description">{{ displayDescription }}</p>
            </div>
            
            <!-- Action button -->
            <button
                v-if="showButton"
                class="empty-action-btn"
                @click="$emit('action')"
                data-testid="empty-state-action-button"
            >
                {{ displayButtonText }}
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'EmptyState',
    langModule: 'key-management',
    props: {
        // Title text
        title: {
            type: String,
            default: ''
        },
        // Description text
        description: {
            type: String,
            default: ''
        },
        // Whether to show action button
        showButton: {
            type: Boolean,
            default: true
        },
        // Button text
        buttonText: {
            type: String,
            default: ''
        }
    },
    computed: {
        displayTitle() {
            return this.title || this.$t('empty_state.title');
        },
        displayDescription() {
            return this.description || this.$t('empty_state.description');
        },
        displayButtonText() {
            return this.buttonText || this.$t('empty_state.button_text');
        }
    },
    emits: ['action']
};
</script>

<style scoped>
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 40px 20px;
    width: 100%;
    max-width: 860px;
    margin: 0 auto;
}

/* Illustration styles */
.empty-illustration {
    width: 140px;
    height: 140px;
    position: relative;
}

.illustration-container {
    width: 100%;
    height: 100%;
    position: relative;
}

/* Background circles */
.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: #FAFBFF;
}

.bg-circle.large {
    width: 120px;
    height: 120px;
    top: 10px;
    left: 10px;
}

.bg-circle.small {
    width: 20px;
    height: 20px;
    top: 0;
    right: 10px;
}

/* Key illustration */
.key-illustration {
    position: absolute;
    top: 30px;
    left: 31px;
    width: 80px;
    height: 80px;
}

.key-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(180deg, #E7EAF9 0%, #F6F7FD 100%);
    border-radius: 50%;
}

.key-body {
    position: relative;
    width: 100%;
    height: 100%;
}

.key-head {
    position: absolute;
    top: 17px;
    left: 27px;
    width: 26px;
    height: 26px;
    background: #FFFFFF;
    border-radius: 50%;
    opacity: 0.5;
}

.key-head::after {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    width: 16px;
    height: 16px;
    background: #FFFFFF;
    border-radius: 50%;
}

.key-shaft {
    position: absolute;
    top: 27px;
    left: 38px;
    width: 4px;
    height: 36px;
    background: #FFFFFF;
    border-radius: 10px;
}

.key-teeth {
    position: absolute;
    top: 55px;
    left: 32.5px;
    width: 8px;
    height: 4px;
    background: #FFFFFF;
    border-radius: 10px;
}

.key-teeth-small {
    position: absolute;
    top: 48px;
    left: 30.5px;
    width: 10px;
    height: 4px;
    background: #FFFFFF;
    border-radius: 10px;
    opacity: 0.6;
}

/* Key tag */
.key-tag {
    position: absolute;
    bottom: 32px;
    right: 23.5px;
    width: 30px;
    height: 30px;
}

.tag-circle {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #FF9A01 0%, #FFB701 100%);
    border-radius: 50%;
    border: 3px solid #FAFBFF;
}

.tag-lines {
    position: absolute;
    top: 7px;
    left: 15px;
    width: 0;
    height: 16px;
}

.tag-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 9px;
    border-left: 4px solid #FFFFFF;
    border-radius: 2px;
}

.tag-line.short {
    top: 12px;
    height: 4px;
}

/* Content styles */
.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    width: 100%;
}

.empty-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    width: 100%;
    text-align: center;
}

.empty-title {
    font-family: 'SF Pro';
    font-weight: 700;
    font-size: 14px;
    line-height: 1.43;
    color: #A0A3AF;
    margin: 0;
}

.empty-description {
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 12px;
    line-height: 1.33;
    color: #C6C8D1;
    margin: 0;
    white-space: pre-line;
    max-width: 600px;
}

.empty-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 5px 15px;
    background: #5D51E2;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.empty-action-btn:hover {
    background: #4A3FBF;
}

.empty-action-btn:active {
    background: #3D3399;
}

/* Responsive design */
@media (max-width: 768px) {
    .empty-state {
        padding: 30px 15px;
        gap: 15px;
    }
    
    .empty-illustration {
        width: 120px;
        height: 120px;
    }
    
    .empty-content {
        gap: 20px;
    }
    
    .empty-title {
        font-size: 13px;
    }
    
    .empty-description {
        font-size: 11px;
        max-width: 400px;
    }
}
</style>
