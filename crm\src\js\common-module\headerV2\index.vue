<template>
    <div class="chime-header-container crm-header crm-header-v2">
        <div class="lofty-onboarding-wrapper" v-if="isOnboardingMode">
            <!-- logo  -->
            <router-link to="/home" class="logo header-logo" custom>
                <img style="width: 100px" :src="logo" v-show="logo" alt="logo" />
            </router-link>

            <div class="ob-center-wrapper">
                <span class="ob-center-wrapper_text">{{ obHeaderText }}</span>

                <div class="ob-center-wrapper__tab" v-if="obTabList.length > 0">
                    <PerformanceTab
                        :tabOption="obTabList"
                        :value="currObTab.value"
                        @change="currObTab.update($event.id)"
                    ></PerformanceTab>
                </div>

                <div class="ob-center-wrapper_back" @click="backDashboard" v-if="isShowBack">
                    <i
                        class="icon2017"
                        :class="[isOnboardingHome ? 'icon-export' : 'icon-back_01']"
                    ></i>

                    <span>{{
                        isOnboardingHome ? $t("header.exploreLofty") : $t("header.backToDashboard")
                    }}</span>
                </div>
            </div>

            <!-- profile picture -->
            <User
                ref="User"
                class="ob-user-wrapper"
                :menuList="personMenuConfigs"
                :isOnboardingMode="isOnboardingMode"
            ></User>
        </div>

        <template v-else>
            <div id="chime-super-user" class="super-user-banner" v-if="SuperUserAlert">
                <div class="left-content">
                    <i class="icon2017 icon-attention_02"></i>
                    <span class="ml10">{{
                        $t("header.superUserTip", { agentName: agentName })
                    }}</span>
                </div>
                <div class="right-content" @click="logout">
                    <i class="icon2017 icon-Logout_03"></i>
                    <span class="ml10">{{ $t("header.logout") }}</span>
                </div>
            </div>

            <div
                id="chime-menu-box"
                class="crm-header-main crm-headerV2-main"
                :style="comHeaderStyle"
            >
                <div id="lofty-works-menus-selector"></div>

                <!-- logo  -->
                <router-link to="/home" class="logo header-logo" custom>
                    <img style="width: 100px" :src="logo" v-show="logo" alt="logo" />
                </router-link>
                <!-- menuList -->
                <Menus
                    v-show="showMenu"
                    :menuList="menuList"
                    :currMenuIndex="currMenuIndex"
                ></Menus>
                <div class="right-box">
                    <button
                        v-if="isShowLoftyApp"
                        class="chime-btn default ghost-transparent lofty-app-btn"
                        @click="toDownloadLoftyApp"
                    >
                        {{ $t("header.getLoftyApp") }}
                    </button>

                    <div class="right-menu" v-if="!isLofty">
                        <template v-for="(menu, index) in rightMenuList">
                            <template v-if="menu.component">
                                <component
                                    class="menu-item"
                                    :is="menu.component"
                                    :config="menu"
                                    :key="index"
                                    v-if="menu.show"
                                ></component>
                            </template>
                            <template v-else-if="menu.clickHandler">
                                <div class="menu-item" :key="index" v-if="menu.show">
                                    <i
                                        class="icon2017 menu-icon"
                                        :number="menu.redPoint"
                                        :class="menu.icon"
                                        @click="menu.clickHandler"
                                    ></i>
                                </div>
                            </template>
                            <template v-else>
                                <div class="menu-item" :key="index" v-if="menu.show">
                                    <i
                                        class="icon2017 menu-icon"
                                        :number="menu.redPoint"
                                        :class="menu.icon"
                                        @click="menuFunc(menu)"
                                    ></i>
                                </div>
                            </template>
                        </template>
                    </div>

                    <!-- profile picture -->
                    <User
                        ref="User"
                        :menuList="personMenuConfigs"
                        @change-tab="currObTab.update($event)"
                    ></User>
                </div>

                <!-- search -->
                <div class="search-box" v-show="showSearch">
                    <Search ref="searchBox" @hideSearch="activeSearch(false)"></Search>
                    <i @click="activeSearch(false)" class="icon2017 icon-cancel_bold"></i>
                </div>

                <div
                    class="ob-back"
                    v-if="isBackOBMode"
                    :style="{ top: SuperUserAlert ? '40px' : '0' }"
                >
                    <button @click="obBackHandler" class="chime-btn mini white-btn">
                        <span class="icon2017 icon-arrow_06_left"></span>
                        {{ $t("header.backOb") }}
                    </button>
                </div>
            </div>
            <Alert
                v-if="isSettingPage && !hideSettingAlert"
                class="alert-default"
                :isCenter="true"
                :showIcon="true"
                :closable="true"
                :showButton="false"
                type="warning"
                :content="$t('header.settingAlertContent')"
                key="settingAlert"
                @closeAlert="closeSettingAlert"
            ></Alert>
            <ProfileTip v-else-if="isLofty" />

            <ObTip
                v-else-if="showObBanner"
                :percent="parseInt(thirdVersionAgentPercent * 100)"
                @open-avatar="openAvatarHandler"
            ></ObTip>

            <template v-else-if="isShowAlert">
                <Alert
                    class="alert-brandads"
                    v-bind="brandAdsBannerCfg"
                    v-if="showBrandAdsBanner"
                    :class="{ 'ob-banner': showObBanner }"
                    @buttonClick="
                        () => {
                            brandAdsBannerCfg.handleButtonClick
                                ? brandAdsBannerCfg.handleButtonClick()
                                : handleButtonClick();
                        }
                    "
                    :beforeClose="handleAdsBeforeClose"
                    @closeAlert="closeBrandAdsAlert"
                    key="brandAdsBannerCfg"
                ></Alert>
                <Alert
                    class="alert-default"
                    v-bind="alertConfig"
                    :class="{ 'ob-banner': showObBanner }"
                    @buttonClick="
                        () => {
                            alertConfig.handleButtonClick
                                ? alertConfig.handleButtonClick()
                                : handleButtonClick();
                        }
                    "
                    @closeAlert="closeAlert"
                    key="alertConfig"
                    v-else
                ></Alert>
            </template>
        </template>
    </div>
</template>
<script>
import { localStore, authority, infoData, basePermission, crmUtils, commonHooks } from "crm";
import { components, popMgr, utils } from "common";
import pop from "./pop/index";
import http from "./api";
import loftyPop from "./components/loftyDownload/index.js";
import ProfileTip from "./components/UpdateProfile/profileTip.vue";
import ObTip from "./components/obTip/index.vue";

import Menus from "./components/menus/index.vue";
import Search from "./components/search/index.vue";
import User from "./components/user/index.vue";
import Help from "./components/help/index.vue";
import {
    obTabList,
    currObTab,
    obHeaderText,
    backDashboard,
    isShowBack,
    isOnboardingHome
} from "@/js/obDashboardV3/hooks/userObHeaderHook.js";
import { getMenuList, getRightMenuList, getPersonMenuList } from "./config";
import { CreateLoftyWorksMenus } from "@chime-org/lofty-menu";

const { Alert, AlertV2, DropDown, PerformanceTab } = components;

const hideSettingAlertKey = "__hideSettingAlert";

export default {
    name: "Header",
    langModule: ["common", "common-header", "common-oncepop"],
    components: {
        Menus,
        Search,
        User,
        Help,
        Alert,
        AlertV2,
        DropDown,
        ProfileTip,
        PerformanceTab,
        ObTip
    },
    props: {
        logo: {
            type: String,
            default: () => ""
        },
        currMenu: {
            type: String,
            default: () => ""
        }
    },
    data() {
        return {
            preOrderLink: "",
            showObBanner: false,
            emailDomainAlertType: "",
            alertConfig: null,
            menuConfigs: getMenuList(),
            rightMenuConfigs: getRightMenuList(),
            personMenuConfigs: getPersonMenuList(),
            showSearch: false,
            onBoardingCfgFromPage: "",
            isBackOBMode: false,
            hasLenderApprove: false,
            isShowSwitchOwner: false,
            lenderApproveLink: [],
            // Parameter information of the top Header
            headerInfo: {
                // The total height of the top Header
                "--H-header": 0,
                // The height of the super banner in the top Header
                "--H-header-super": 0,
                // The height of the menu of the top navigation Header
                "--H-header-menu": 0
            },

            obBannerCfg: {},

            brandAdsBannerCfg: null,

            isOnboardingMode: false,

            thirdVersionAgentPercent: 0,

            show: true,
            hideSettingAlert: localStore.getLocal(hideSettingAlertKey)
        };
    },
    setup() {
        return {
            obTabList,
            currObTab,
            obHeaderText,
            backDashboard,
            isShowBack,
            isOnboardingHome,
            hasUnpaidBill: commonHooks.useGlobalHooks.hasUnpaidBill
        };
    },
    watch: {
        $route: {
            immediate: true,
            handler(to) {
                const { name } = to;
                this.isOnboardingMode = name === "obDashboardV3";
            }
        },
        onBoardingCfgFromPage: {
            immediate: true,
            handler() {
                this.handleOnBoardingBack();
            }
        },
        isOnboardingMode: {
            immediate: true,
            handler(val) {
                if (!val) {
                    this.initLoftyWorksMenus();
                }
            }
        }
    },
    computed: {
        isSettingPage() {
            return this.$route.path.includes("/usersetting");
        },
        settingAlertConfig() {
            return {};
        },
        isVersionThree() {
            return infoData.isOBVThree;
        },
        isNewObDashboard() {
            if (this.isOnboardingMode) {
                return infoData.isNewObDashboard;
            }

            return infoData.isNewObDashboard;
        },
        showMenu() {
            if (this.isVersionThree) {
                return !this.showSearch && !this.isBackOBMode;
            }

            if (this.isNewObDashboard) {
                return false;
            }

            return !this.showSearch && !this.isBackOBMode;
        },
        isBuyChristmasSphereAds() {
            return (
                window.__crmRouter.currentRoute.name == "buySphereAdsForFestival" &&
                !window.__crmRouter.currentRoute.query.draftId
            );
        },
        isBuyChristmasListingAds() {
            return (
                window.__crmRouter.currentRoute.name == "buyListingPromotion" &&
                window.__crmRouter.currentRoute.query?.isChristmasPromotion == "true"
            );
        },
        comHeaderStyle() {
            return {
                borderBottom:
                    this.isShowAlert ||
                    this.isBuyChristmasSphereAds ||
                    this.isBuyChristmasListingAds
                        ? "none"
                        : "1px solid #e1e2e6"
            };
        },
        isShowAlert() {
            if (this.isNewObDashboard) {
                return false;
            }
            return (
                (this.alertConfig && !this.getAlertHideFlag && !this.isLofty) ||
                this.showBrandAdsBanner
            );
        },
        getAlertHideFlag() {
            return this.menuList[this.currMenuIndex]?.hideAlert;
        },
        hasBilling() {
            return infoData.getUserInfo().hasBilling;
        },
        // 用户是否有backoffce
        hasBackOffice() {
            // return true;
            const { hasBackOffice: Backoffice } = infoData.userInfo;
            return !!Backoffice;
        },
        currMenuIndex() {
            const currentPath = this.$route.path;
            const menu = this.menuList.find((menu) => {
                if (menu.toObj?.path === currentPath) {
                    return true;
                }
                if (menu.children) {
                    return menu.children.some((child) => child.toObj?.path === currentPath);
                }
                return false;
            });

            return this.menuList.findIndex((item) => item.name === menu?.name);
        },
        SuperUserAlert() {
            return infoData.getUserInfo()?.superAdmin && !this.isLofty;
        },
        agentName() {
            return infoData.getUserInfo()?.name;
        },
        isLofty() {
            return infoData.getUserInfo().isLofty;
        },
        isShowLoftyApp() {
            const { isDownloadLoftyApp } = infoData.getUserInfo();
            const { LoftyChat } = basePermission;
            /**
             * 1、Did not download lofty App
             * 2、It’s a lofty account
             */
            return this.isLofty && !isDownloadLoftyApp && LoftyChat;
        },
        showBrandAdsBanner() {
            return this.currMenu === "campaign" && this.brandAdsBannerCfg;
        },
        menuList() {
            return (this.menuConfigs ?? []).map((menu) => {
                return {
                    ...menu,
                    icon: this.getIconCls(menu)
                };
            });
        },
        rightMenuList() {
            return (this.rightMenuConfigs || []).map((menu) => {
                let iconClass = this.getIconCls(menu);
                if (menu.name === "workspace") {
                    if (this.$route.name === "ChatWindow") {
                        iconClass.push("active");
                    } else {
                        iconClass = iconClass.filter((cls) => cls !== "active");
                    }
                }

                return {
                    ...menu,
                    icon: iconClass
                };
            });
        }
    },
    methods: {
        initLoftyWorksMenus() {
            this.$nextTick(() => {
                new CreateLoftyWorksMenus({
                    el: document.querySelector("#lofty-works-menus-selector"),
                    config: {
                        mode: "stage",
                        fetch: crmUtils.sendAjax
                    }
                });
            });
        },
        async handleAdsBeforeClose() {
            this.brandAdsBannerCfg = null;
            const configRes = await this.getAlertConfig({ skipAds: true });
            if (configRes) {
                this.alertConfig = configRes;
                return true;
            }
            return false;
        },
        async handleTextClick() {
            const { ok } = await popMgr.confirm({
                title: "",
                desc: this.$t("header.warning.toast2"),
                maxWidth: "480px",
                cls: "test-confirm",
                okText: this.$t("popWin.confirmText"),
                cancelText: this.$t("popWin.cancelText"),
                btnAlign: "right",
                noTitle: true
            });
            if (ok) {
                crmUtils
                    .sendAjax({
                        key: "/api/dialer-server/banner/blacklist/save",
                        url: `/api/dialer-server/banner/blacklist/save/${infoData.userInfo.teamId}`,
                        type: "POST"
                    })
                    .then((_) => {
                        this.alertConfig = null;
                    });
            }
        },
        toDownloadLoftyApp() {
            loftyPop.createLoftyDownLoadPop();
        },
        getHeaderParams() {
            const rootParams = [
                {
                    key: "--H-header",
                    selector: ".chime-header-container"
                },
                {
                    key: "--H-header-super",
                    selector: "#chime-super-user"
                },
                {
                    key: "--H-header-menu",
                    selector: "#chime-menu-box"
                }
            ];

            rootParams.forEach((item) => {
                const { key, selector } = item;
                const ele = document.querySelector(selector);
                const offsetHeight = ele?.offsetHeight || ele?.clientHeight || 0;

                this.headerInfo[key] = offsetHeight;
                document.documentElement.style.setProperty(key, `${offsetHeight}px`);
            });
        },
        resetOnBoardingCfgPage() {
            this.onBoardingCfgFromPage = this.$route.query?.fromPage;
            this.handleOnBoardingBack();
        },
        async init() {
            this.alertConfig = await this.getAlertConfig();
        },
        async getAlertConfig({ skipAds } = {}) {
            let configRes = null;
            // 缴费的优先级最高 别写在顶部
            //banner: Pay the fees > lender approve > email domain
            // 1.缴费、账单相关banner
            configRes = this.handleBilling();
            // 如果有生成banner的配置项，则阻止往下执行
            if (configRes) {
                return configRes;
            }

            // if (!skipAds) {
            //     this.brandAdsBannerCfg = await this.getDraftBrandAds();
            // }

            // configRes = await this.handleBloomBindCard();

            // if (configRes) {
            //     return configRes;
            // }

            // ob banner priority: higher  && only show in ob dashboard
            // todo 90 day && home && from !==ob from obDashboard to home dashboard
            const from = crmUtils.getParameterByName("from");
            this.showObBanner = false;
            if (from !== "ob") {
                const { obDashboardInfo } = infoData;
                const showTip = obDashboardInfo.showBanner;
                this.showObBanner = infoData.isOBVThree && showTip;
            }

            if (configRes) {
                return configRes;
            }

            // switch owner
            configRes = await this.handleSwitchOwner();
            if (configRes) {
                return configRes;
            }

            // 2.email domain相关banner（优先级在欠费后面）
            // 此种banner类型包含：SOME: some domain inactive; ALL: all domain inactive; ONLY_CHIME: only chime.house
            const [_err1, _data1] = await crmUtils.awaitWrap(
                crmUtils.sendAjax({
                    url: "/api/email-server/email/domain/banner"
                })
            );
            configRes = this.handleEmailDomain(_err1, _data1);
            // 如果有生成banner的配置项，则阻止往下执行
            if (configRes) {
                return configRes;
            }

            configRes = this.brandAdsBannerCfg;
            if (configRes) {
                return configRes;
            }

            return configRes;
        },

        // brand ads提示 campaign页面优先级最高 其他页面优先级最后
        async getDraftBrandAds() {
            const { userId } = infoData.getUserInfo();
            const draftList = await crmUtils
                .sendAjax({
                    url: `/api/adsplatform/sphere/query-drafts?agentId=${userId}`
                })
                .then((res) => {
                    return res?.data || [];
                })
                .catch((err) => {
                    return [];
                });
            if (draftList.length) {
                return {
                    containerCls: "header-banner",
                    content: this.$t("header.brandAdsTip", {
                        count: draftList.length
                    }),
                    showType: "warning",
                    isCenter: true,
                    closable: true,
                    showButton: true,
                    buttonText: this.$t("header.warning.btnText"),
                    handleButtonClick: () => {
                        const draftIdList = draftList.map((i) => i.id).join(",");
                        const link =
                            crmUtils.commonUrl + `/campaigns/buySphereAds?draftIds=${draftIdList}`;
                        window.open(link, "_blank");
                    }
                };
            }
            return null;
        },
        handleBilling() {
            if (!infoData.getUserInfo().hasBilling) {
                return null;
            }
            let _msg = "";
            const _payFailInfo = infoData.getUserInfo().payFailInfo;
            const _14Day = 14 * 24 * 3600 * 1000;
            const _1Day = 1 * 24 * 3600 * 1000;

            const _isPastDue = _payFailInfo?.isPastDue;
            const _hasBilling = _payFailInfo?.billings?.length;

            const _hasPercentBilling = _payFailInfo?.billings?.some((v) => v.businessType == 1);
            const _hasMounthlyBilling = _payFailInfo?.billings?.some((v) => v.businessType == 3);
            const _hasAIBilling = _payFailInfo?.billings?.some((v) => v.businessType == 8);
            const _hasRevaluateBilling = _payFailInfo?.billings?.some((v) => v.businessType == 22);

            if (_hasBilling) {
                _msg = this.$t("header.payToast");
            }

            if (infoData.getUserInfo().isTeamOwner) {
                // 支付账户状态为Past Due
                // 支付账户有未被abolished的付费失败的按比例billing
                if (_isPastDue && _hasPercentBilling) {
                    _msg = this.$t("header.payToast");
                }
                // 支付账户状态为Past Due
                // 支付账户有未被abolished的月付失败的billing
                // Payment failed 1-14天
                if (_isPastDue && _hasMounthlyBilling) {
                    const _mounthlyBillCreatedTime = _payFailInfo?.billings.filter(
                        (v) => v.businessType == 3
                    )[0]?.createTime;
                    const _currentDate = new Date().getTime();
                    if (_currentDate < _14Day + _mounthlyBillCreatedTime) {
                        _msg = this.$t("header.payToast");
                    }
                }
                // 支付账户有未被abolished的付费失败的AI激活费billing/Revaluate billing
                if (_hasAIBilling || _hasRevaluateBilling) {
                    _msg = this.$t("header.payToast");
                }
                // 支付账户有未被abolished的月付失败的billing
                if (_hasMounthlyBilling) {
                    const _expiredDate = new Date(+_payFailInfo.endTime).toLocaleDateString(
                        "en-US",
                        {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric"
                        }
                    );
                    _msg = this.$t("header.billingToast", { _expiredDate });
                }
            } else {
                // 支付账户有未被abolished的付费失败的AI激活费billing/Revaluate billing
                if (_hasAIBilling || _hasRevaluateBilling) {
                    _msg = this.$t("header.payToast");
                }
                // 支付账户有未被abolished的月付失败的billing
                if (_hasMounthlyBilling) {
                    const _expiredDate = new Date(+_payFailInfo.endTime).toLocaleDateString(
                        "en-US",
                        {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric"
                        }
                    );
                    _msg = this.$t("header.billingToast", { _expiredDate });
                }
            }

            if (_payFailInfo && _msg) {
                this.hasUnpaidBill = true;
                return {
                    containerCls: "header-banner",
                    content: `<img src="https://static.chimeroi.com/servicetool-temp/money_01.svg" class="chime-alert__icon">${_msg}`,
                    showType: "danger",
                    buttonText: this.$t("header.payBtn")
                };
            } else {
                this.hasUnpaidBill = false;
                return null;
            }
        },
        async handleBloomBindCard() {
            const [_resErr, resData] = await crmUtils.awaitWrap(http.getBloomBindCardStatus());
            if (_resErr) {
                console.log(_resErr);
            }

            if (!resData?.data?.show) {
                return null;
            }
            return {
                containerCls: "header-banner",
                content: `<img src="https://static.chimeroi.com/servicetool-temp/money_01.svg" class="chime-alert__icon"> ${this.$t(
                    "header.warning.bloomBindCardTip"
                )}`,
                showType: "warning",
                buttonText: this.$st("common-oncepop", "oncePop.billing.bindBtn"),
                handleButtonClick: () => {
                    window.__crmRouter.push({
                        name: "buyLoftyBloom",
                        query: {
                            processCode: resData?.data?.processCode || ""
                        }
                    });
                }
            };
        },
        handleEmailDomain(_err1, _data1) {
            if (!_err1 && _data1?.data?.type) {
                const type = _data1.data.type;
                this.emailDomainAlertType = type;
                if (type === "NONE") {
                    return null;
                }
                // manage user权限
                const isManager =
                    authority.checkUserRight("MANAGE_TEAM_AGENT") ||
                    authority.checkUserRight("MANAGE_TEAM_AGENT_DEPARTMENT");
                const descContent = {
                    SOME: this.$t("header.warning.desc1"),
                    ALL: this.$t("header.warning.desc2"),
                    ONLY_CHIME: this.$t("header.warning.desc3")
                };
                this.preOrderLink = "/admin/home/<USER>/manageDomain";
                const btnText =
                    type === "ONLY_CHIME"
                        ? this.$t("header.warning.btnText")
                        : this.$t("learnMore");
                return {
                    containerCls: "header-banner",
                    content: descContent[type],
                    showType: "warning",
                    isCenter: true,
                    closable: true,
                    showButton: isManager,
                    buttonText: btnText
                };
            } else {
                return null;
            }
        },

        async handleSwitchOwner() {
            const [err, res] = await crmUtils.awaitWrap(
                crmUtils.sendAjax({
                    url: "/api/user-web/switch-owner/status"
                })
            );
            this.isShowSwitchOwner = res?.data && Object.keys(res?.data).length > 0;
            if (err || !res?.data) {
                return null;
            }
            const { userId } = infoData.getUserInfo();
            const showBanner = res?.data?.newOwnerId === userId;
            if (showBanner) {
                let contentText = this.$t("header.warning.switchOwnerBanner", {
                    agentName: res?.data?.oldOwnerName
                });
                return {
                    containerCls: "header-banner",
                    content: contentText,
                    showType: "warning",
                    buttonText: this.$t("header.warning.accept"),
                    isCenter: true
                };
            } else {
                return null;
            }
        },
        getIconCls(config) {
            let redPointCls = "",
                redPoint = config.redPoint;
            if (redPoint !== false) {
                redPointCls =
                    typeof redPoint === "boolean"
                        ? "red-point"
                        : /^\d+[\d|+]?$/g.test(redPoint.toString())
                        ? "red-number"
                        : "red-text";
            }
            let active = config.active ? "active" : "";
            return [config.icon, redPointCls, active];
        },
        closeBrandAdsAlert() {
            this.brandAdsBannerCfg = null;
            this.closeAlert();
        },
        closeSettingAlert() {
            localStore.setLocal(hideSettingAlertKey, true);
            this.hideSettingAlert = true;
        },
        async closeAlert() {
            this.alertConfig = null;
            // 只有emailDomainAlertType为“ONLY_CHIME”的alert关闭时才需要调用接口
            if (this.emailDomainAlertType === "ONLY_CHIME") {
                const [_err, _data] = await crmUtils.awaitWrap(
                    crmUtils.sendAjax({
                        url: "/api/email-server/email/domain/banner",
                        type: "DELETE"
                    })
                );
                console.log(_err);
            }
            setTimeout(() => {
                this.getHeaderParams();
            }, 500);
        },
        async handleButtonClick() {
            if (this.isShowSwitchOwner) {
                const [err, res] = await crmUtils.awaitWrap(
                    crmUtils.sendAjax({
                        url: "/api/user-web/switch-owner/accept",
                        type: "PUT"
                    })
                );
                if (err || res?.status?.code !== 0) {
                    utils.toast({
                        content: res?.status?.msg
                    });
                    return;
                }

                if (res?.status?.code === 0) {
                    utils.toast({
                        content: this.$t("header.accountUpdate")
                    });
                    this.init();
                    return;
                }
            }
            //lender approve
            if (this.hasLenderApprove) {
                this.lenderApproveLink.forEach((link) => {
                    window.open(link, "_blank");
                });
                return;
            }
            // emailDomainAlertType是”SOME“或者”ALL“则需要先弹出二次提示框
            if (this.emailDomainAlertType && this.emailDomainAlertType !== "ONLY_CHIME") {
                const { ok } = await popMgr.confirm({
                    title: "",
                    desc: this.$t("header.warning.toast1"),
                    maxWidth: "500px",
                    cls: "test-confirm",
                    okText: this.$t("header.warning.btnText1"),
                    cancelText: "",
                    btnAlign: "right",
                    noTitle: true // 是否没有title
                });
                if (ok) {
                    window.open(this.preOrderLink, "_blank");
                }
                return;
            }
            if (this.preOrderLink) {
                window.open(this.preOrderLink, "_blank");
                return;
            }
            let billingMoney = 0;
            const _payFailInfo = infoData.getUserInfo().payFailInfo;
            const _totalTax = _payFailInfo?.totalTax;
            billingMoney = `${_payFailInfo?.totalChargePrice?.toFixed(2)}`.replace(
                /\d{1,3}(?=(\d{3})+(\.\d+)?$)/g,
                "$&,"
            );

            pop.createForcePayPop({
                popWinConfig: {
                    isShowTitle: true,
                    title: this.$t("header.warning.title"),
                    isUserScroller: false,
                    isLenderPayFailed: false,
                    lenderPayFailedObj: null
                },
                totalTax: _totalTax,
                softForcePay: true,
                displayMsg: this.$t("header.warning.msg", {
                    billingMoney
                }),
                calcTax: async (context) => {
                    const _payFailInfo = await infoData.updatePayFailInfo();
                    context["payFailInfo"] = _payFailInfo;
                    context["totalTax"] = _payFailInfo.totalTax;
                    let _billingMoney = 0;
                    _billingMoney = `${_payFailInfo?.totalChargePrice?.toFixed(2)}`.replace(
                        /\d{1,3}(?=(\d{3})+(\.\d+)?$)/g,
                        "$&,"
                    );
                    context["displayMsg"] = this.$t("header.warning.msg", {
                        billingMoney: _billingMoney
                    });
                }
            }).then((data) => {
                this.handleBillingAfterPaid(data);
            });
        },
        handleBillingAfterPaid(data) {
            if (data?.type == "update") {
                this.init();
            } else if (data?.type == "close") {
                // billing 页面则刷新页面，使billing模块同步数据
                if (
                    window.location.pathname.match(/\/admin\/home\/billing/) ||
                    window.location.pathname.match(/\/admin\/home\/<USER>\/Revaluate/)
                ) {
                    setTimeout(() => window.location.reload(), 1500);
                }
            }
        },
        logout() {
            // super user从agent账户退出以后 需要进入agent list页面
            const backUrl = `/admin/home/<USER>/agent`;
            this.$refs?.User?.logout(backUrl);
        },
        menuFunc(menu) {
            switch (menu?.name) {
                case "search":
                    this.activeSearch(true);
                    break;
                default:
                    break;
            }
        },
        activeSearch(flag) {
            this.showSearch = flag;
            if (!flag) {
                setTimeout(() => {
                    if (this.$refs?.searchBox) {
                        this.$refs.searchBox.searchKey = "";
                    }
                });
            } else {
                this.$refs?.searchBox?.focusInput();
            }
        },
        obBackHandler() {
            const onBoardingCfgFromPage = this.onBoardingCfgFromPage;

            this.onBoardingCfgFromPage = "";

            if (this.isVersionThree) {
                window.__crmRouter.push({
                    name: "obDashboardV3",
                    query: {
                        step: onBoardingCfgFromPage
                    }
                });

                return;
            }

            try {
                window.__crmRouter.push({
                    name: "home",
                    query: {
                        from: onBoardingCfgFromPage
                    }
                });
            } catch (error) {
                console.log(error);
                window.location.href = "/admin/home/<USER>" + onBoardingCfgFromPage;
            }
        },

        openAvatarHandler() {
            this.$refs?.User?.showHandler?.(true);
            this.showObBanner = false;
        },
        handleOnBoardingBack() {
            const fromPage = this.onBoardingCfgFromPage;
            this.isBackOBMode = !!fromPage;

            const { obDashboardInfo } = infoData;
            this.thirdVersionAgentPercent = obDashboardInfo.agentProgress;
        }
    },
    mounted() {
        this.onBoardingCfgFromPage = this.$route.query?.fromPage;
        this.handleOnBoardingBack();
        this.init();
        this.getHeaderParams();
        this.initLoftyWorksMenus();
    },
    updated() {
        setTimeout(() => {
            this.getHeaderParams();
        }, 300);
    }
};
</script>
<style lang="less">
.chime-header-container {
    position: relative;
    width: 100%;
    background-color: #fff;
    padding: 0 20px;
    box-sizing: border-box;
    min-width: 485px;
    z-index: 9;

    .lofty-onboarding-wrapper {
        position: relative;
        width: 100%;
        height: 60px;
        overflow: hidden;
        box-sizing: border-box;
        padding: 0 15px 0 20px;
        position: relative;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #ebecf1;

        .logo {
            margin-right: 22px;
            height: 30px;
            cursor: pointer;
            flex-shrink: 0;

            img {
                height: 100%;
                vertical-align: middle;
                max-width: 180px;
                object-fit: contain;
            }
        }

        .ob-center-wrapper {
            flex: 1;
            overflow: hidden;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &__tab {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }

            &_text {
                color: #202437;
                font-size: 15px;
                line-height: 24px;
                font-weight: 500;
            }
            &_back {
                cursor: pointer;
                display: flex;
                align-items: center;

                &:hover {
                    span {
                        text-decoration: underline;
                    }
                }
                i {
                    font-size: 16px;
                    margin-right: 12px;
                    color: var(--primary-color);
                }
                span {
                    color: var(--primary-color);
                    font-size: 15px;
                    line-height: 24px;
                    font-weight: 400;
                }
            }
        }

        .ob-user-wrapper {
            margin-left: 20px;

            .profile-picture {
                .user-logo {
                    left: 0;
                }
            }
        }
    }
}

.header-main.with-header {
    display: block;

    .crm-header {
        display: block;
        padding: 0;
    }
    .crm-header-main {
        display: flex;
        padding: 0 20px;
    }
}

.crm-header {
    background: var(--upgrade-header-bg-Color) !important;
    &.theme-lucido {
        border-bottom: none;

        .lofty-onboarding-wrapper {
            .ob-center-wrapper {
                &_text {
                    color: #ffffff;
                }
                &_back {
                    i {
                        color: #ffffff;
                    }
                    span {
                        color: #ffffff;
                    }
                }
            }
        }
        .icon.icon-sel-triangle,
        .smallList .icon2017 {
            color: white;
            &::before {
                color: white;
            }
        }
    }
    .header-banner {
        margin-left: -20px;
        margin-right: -20px;
        flex-basis: 100%;
        height: 50px;
        line-height: 50px;
        box-sizing: border-box;
        justify-content: center;
        padding-left: 35px;
        padding-right: 35px;
        &.ob-banner {
            margin-left: 0px;
            margin-right: 0px;
            .chime-alert__closebtn {
                right: 30px;
            }
        }
        &.danger {
            color: #f0454c;
            border: none;
            border-radius: 0;
            border-top: 1px solid #fcd7d2;
            border-bottom: 1px solid rgba(0, 0, 0, 0);
            .chime-alert__btn {
                background-color: #f0454c;
                margin-left: 20px;
                line-height: 21px;
                min-width: 87px;
                height: 30px;
                &:hover {
                    opacity: 0.9;
                }
            }
        }
        .chime-alert__closebtn {
            height: 14px;
            line-height: 14px;
            right: 35px;
            top: 18px;
        }
        // 顶部alert兼容小屏
        .chime-alert__content {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        &.is-closable {
            // 顶部alert兼容小屏
            padding-right: 60px;
        }
    }
    .super-user-banner {
        padding: 0px 20px;
        background: rgba(32, 36, 55, 0.95);
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        color: white;
        height: 40px;
        i {
            color: rgba(255, 255, 255, 0.5);
            font-size: 16px;
            line-height: 1em;
        }
        span {
            line-height: 1em;
        }
        .left-content,
        .right-content {
            display: flex;
            align-items: center;
        }
        .right-content {
            cursor: pointer;
            color: white;
            i {
                color: white;
            }
        }
    }
    .crm-header-main.crm-headerV2-main {
        height: 60px;
        line-height: normal;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px 0 20px;
        position: relative;
        box-sizing: border-box;

        .extra-space {
            flex: 1;
        }
        .com-dropdown .arrow {
            width: 8px;
            height: 8px;
            top: 4.5px;
        }
        #lofty-works-menus-selector {
            margin-right: 15px;
            flex-shrink: 0;
        }
        .logo {
            margin-right: 33px;
            height: 30px;
            cursor: pointer;
            > img {
                height: 100%;
                vertical-align: middle;
                max-width: 180px;
                object-fit: contain;
            }
        }
        &.gs .logo {
            width: 80px;
        }
        .right-menu {
            margin-right: 5px;
            display: flex;
            margin-top: 3px;
            flex: 1;
            justify-content: flex-end;
            flex-shrink: 0;
            flex-wrap: nowrap;
            min-width: 200px;
            .menu-item {
                margin: 0 10px;
                &:hover {
                    .menu-icon {
                        color: var(--upgrade-header-icon-hover-Color);
                        transform: rotate(360deg);
                    }
                }
                .menu-icon {
                    cursor: pointer;
                    font-size: 20px;
                    color: var(--upgrade-header-icon-Color);
                    transition: all 0.3s ease-in-out;
                    &.active {
                        color: var(--upgrade-header-icon-hover-Color);
                    }
                }
            }
        }
        .to-back-office {
            padding-left: 25px;
            margin-left: 8px;
            position: relative;
            &::before {
                content: "";
                display: block;
                width: 1px;
                height: 20px;
                background-color: #e1e2e6;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                left: -1px;
            }
            .icon-backoffice {
                font-size: 24px;
                color: #b5bacc;
            }
        }
        .common-red {
            content: attr(number);
            position: absolute;
            z-index: 2;
            border: 1px solid #fff;
            color: #fff;
            background: #f0454c;
            display: inline-block;
        }
        .red-number,
        .red-text {
            position: relative;
        }
        // number 红泡
        .red-number::after {
            .common-red();
            top: -5px;
            left: calc(100% - 9px);
            font-size: 12px;
            line-height: 14px;
            height: 14px;
            border-radius: 7px;
            padding: 0 4px;
            border-bottom-left-radius: 4px;
        }
        // new 红泡
        .red-text::after {
            .common-red();
            font-size: 20px;
            height: 28px;
            line-height: 28px;
            border-radius: 14px;
            border-bottom-left-radius: 8px;
            transform: scale(0.5); //10px
            top: -10px;
            left: unset;
            padding: 0 8px;
            margin-right: -13px;
            border: none;
        }
        &.gf {
            .logo {
                width: 135px;
            }
        }
        .search-box {
            width: 100%;
            height: 100%;
            background: var(--upgrade-header-bg-Color);
            position: absolute;
            left: 0;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 99999;
            > i {
                position: absolute;
                left: 50%;
                top: 50%;
                margin-top: -20px;
                margin-left: 330px;
                display: inline-block;
                text-align: center;
                width: 40px;
                height: 40px;
                line-height: 38px;
                border: 1px solid var(--upgrade-header-icon-Color);
                border-radius: 50%;
                box-sizing: border-box;
                color: var(--upgrade-header-search-Color);
                font-size: 14px;
                cursor: pointer;
                &:hover {
                    color: var(--upgrade-header-search-Color);
                    box-shadow: 0px 1px 3px rgba(0, 10, 30, 0.1);
                }
                &:active {
                    color: var(--upgrade-header-search-Color);
                    background: #ebecf1;
                }
            }
        }

        .ob-back {
            width: 100%;
            height: 60px;
            background: #fff;
            position: absolute;
            left: 0;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding-left: 20px;
            z-index: 999999;
            box-sizing: border-box;
            border-bottom: 1px solid rgb(225, 226, 230);
        }
    }
}
.crm-header-main.crm-headerV2-main {
    .extra-space {
        flex: 1;
    }
    .right-box {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        height: 100%;
        > .com-dropdownbox {
            width: 30px;
            overflow: hidden;
        }

        .lofty-app-btn {
            margin-right: 20px;
            height: 30px;
        }
    }
}
@media (max-width: 1280px) {
    .crm-header-main {
        height: 60px;
    }
    .crm-header-main .logo {
        margin-right: 33px;
    }
    .crm-header-main .menu-list .icon2017 {
        font-size: 20px;
        margin: 0 12px;
    }
}
@media screen and (min-width: 1180px) and (max-width: 1320px) {
    .crm-header-main .header-search-wrap .header-search-result {
        width: 215px;
        // width: 438px;
    }
    .crm-header-main .header-search-wrap.active .search-input {
        width: 175px;
    }
}
@media screen and (min-width: 741px) and (max-width: 1180px) {
    .crm-header-main .header-search-wrap .header-search-result {
        width: 200px;
        // width: 400px;
    }
    .crm-header-main .header-search-wrap.active .search-input {
        width: 160px;
    }
}
@media screen and (max-width: 740px) {
    .crm-header-main .header-search-wrap .header-search-result {
        width: ~"calc(100vw - 280px)";
    }
    .crm-header-main .header-search-wrap.active .search-input {
        width: ~"calc(100vw - 320px)";
    }
}
</style>

<style lang="less" scoped>
.chime-header-container {
    .alert-10dlc {
        :deep(.chime-alert-v2-content_text) {
            a {
                color: #41399f;
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}

.alert-default,
.alert-10dlc,
.alert-ob,
.alert-brandads {
    border-radius: 0px;
    border-top-width: 0;
    border-right-width: 0;
    border-left-width: 0;
    border-bottom-width: 1px;
}
</style>
