<template>
    <div class="property-item" data-testid="property-item-container">
        <div class="property-image" data-testid="property-item-image">
            <img :src="imageUrl" :alt="property.address" />
        </div>
        <div class="property-info">
            <div class="property-address" :title="propertyAddress" data-testid="property-item-address">
                {{ propertyAddress }}
            </div>
            <div class="property-id" data-testid="property-item-id">
                {{ $t('property.id_prefix') }} {{ propertyId }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PropertyItem',
    langModule: 'key-management',
    props: {
        property: {
            type: Object,
            required: true
        }
    },
    computed: {
        imageUrl() {
            // Prioritize API returned image URL, otherwise use random image
            if (this.property.photo_url) {
                return this.property.photo_url;
            }
            // Use random image service, generate fixed random image based on keyId
            const seed = this.property.id || this.property.keyId || Math.random();
            return `https://picsum.photos/seed/${seed}/64/48`;
        },
        propertyAddress() {
            // Compatible with new and old data formats
            return this.property.property_address || this.property.address || '';
        },
        propertyId() {
            // Compatible with new and old data formats
            return this.property.id || this.property.keyId || '';
        }
    }
};
</script>

<style scoped>
.property-item {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.property-image {
    flex-shrink: 0;
}

.property-image img {
    width: 64px;
    height: 48px;
    border-radius: 6px;
    object-fit: cover;
}

.property-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
}

.property-address {
    font-family: 'SF Pro Text';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.property-id {
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 12px;
    line-height: 1.33;
    color: #797E8B;
}
</style>
