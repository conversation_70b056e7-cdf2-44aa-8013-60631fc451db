<template>
    <div class="key-management-page" data-testid="key-management-page-container">
        <div class="page-header-section">
            <div class="page-header-content">{{ $t('page.title') }}</div>
        </div>
        <div class="key-management-wrapper">
            <div class="counts-section">
                <KeyStatistics />
            </div>

            <SearchFilter
                @search-change="handleSearchChange"
                @filter-change="handleFilterChange"
            />

            <!-- Loading state -->
            <div v-if="loading" class="loading-container" data-testid="key-management-loading-container">
                <div class="loading-spinner">{{ $t('loading.text') }}</div>
            </div>

            <!-- Data table or empty state -->
            <div v-else class="table-container" data-testid="key-management-table-container">
                <!-- Show table and pagination when data exists -->
                <template v-if="keyList.length > 0">
                    <KeyTable :data="keyList" />

                    <!-- Pagination component -->
                    <div class="pagination-container" data-testid="key-management-pagination-container">
                        <Pagination
                            class="key-list-pagination"
                            @change="handlePaginationChange"
                            :activeIndex="pagination.page"
                            :pageSize="pagination.limit"
                            :totalPage="totalPage"
                            :pageSizeList="pageSizeList"
                            :showPageInput="true"
                            :showPageSize="true"
                            :showGuideArrow="true"
                            :rightPageNumber="1"
                        />
                    </div>
                </template>

                <!-- Empty state (shown when no data or error) -->
                <EmptyState
                    v-else
                    :title="emptyStateConfig.title"
                    :description="emptyStateConfig.description"
                    :showButton="emptyStateConfig.showButton"
                    :buttonText="emptyStateConfig.buttonText"
                    @action="handleEmptyStateAction"
                />
            </div>
        </div>
    </div>
</template>

<script>
import SearchFilter from './components/SearchFilter.vue';
import KeyTable from './components/KeyTable.vue';
import KeyStatistics from './components/KeyStatistics.vue';
import EmptyState from './components/EmptyState.vue';
import { components } from 'common';
import keyApi from './api.js';

const { Pagination } = components;

export default {
    name: 'KeyManagement',
    langModule: 'key-management',
    components: {
        SearchFilter,
        KeyTable,
        KeyStatistics,
        EmptyState,
        Pagination
    },
    data() {
        return {
            // Search and filter conditions
            searchKeyword: '',
            filters: {
                status: 'all',
                holder_id: '',
                property_type: 'all'
            },

            // Pagination related
            pagination: {
                page: 1,
                limit: 20,
                total: 0
            },
            pageSizeList: [10, 20, 50, 100],

            // Data related
            keyList: [],
            loading: false,

            // Search debounce timer
            searchTimer: null
        };
    },
    computed: {
        // Calculate total pages
        totalPage() {
            return Math.ceil(this.pagination.total / this.pagination.limit);
        },

        // Empty state configuration
        emptyStateConfig() {
            return {
                title: this.$t('empty_state.title'),
                description: this.$t('empty_state.description'),
                showButton: true,
                buttonText: this.$t('empty_state.button_text')
            };
        }
    },
    mounted() {
        // Load data when page is mounted
        this.loadKeyList();
    },
    beforeDestroy() {
        // Clean up search debounce timer
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
    },
    methods: {
        /**
         * Load key list data from API
         */
        async loadKeyList() {
            this.loading = true;

            try {
                // Build API parameters
                const params = {
                    page: this.pagination.page,
                    limit: this.pagination.limit
                };

                // Add search keyword
                if (this.searchKeyword.trim()) {
                    params.search = this.searchKeyword.trim();
                }

                // Add filter conditions
                if (this.filters.status !== 'all') {
                    params.status = this.filters.status;
                }
                if (this.filters.holder_id) {
                    params.holder_id = this.filters.holder_id;
                }
                if (this.filters.property_type !== 'all') {
                    params.property_type = this.filters.property_type;
                }

                // Call API
                const response = await keyApi.getKeyList(params);

                if (response.success) {
                    this.keyList = response.data;
                    this.pagination.total = response.total;
                } else {
                    // On error, show empty state (no error message)
                    console.warn('Failed to load key list:', response.error);
                    this.keyList = [];
                    this.pagination.total = 0;
                }
            } catch (error) {
                // On error, show empty state (no error message)
                console.error('Failed to load key list:', error);
                this.keyList = [];
                this.pagination.total = 0;
            } finally {
                this.loading = false;
            }
        },

        /**
         * Handle search change with debounce
         */
        handleSearchChange(keyword) {
            this.searchKeyword = keyword;

            // Clear previous timer
            if (this.searchTimer) {
                clearTimeout(this.searchTimer);
            }

            // Set debounce delay
            this.searchTimer = setTimeout(() => {
                // Reset to first page and reload data
                this.pagination.page = 1;
                this.loadKeyList();
            }, 500); // 500ms debounce delay
        },

        /**
         * Handle filter change
         */
        handleFilterChange(filters) {
            this.filters = { ...filters };
            // Reset to first page and reload data
            this.pagination.page = 1;
            this.loadKeyList();
        },

        /**
         * Handle pagination change
         */
        handlePaginationChange({ page, size }) {
            this.pagination.page = page;
            this.pagination.limit = size;
            this.loadKeyList();
        },

        /**
         * Handle empty state action
         */
        handleEmptyStateAction() {
            // Handle add first key action
            console.log('Add first key action triggered');
            // TODO: Implement add key functionality
        }
    }
};
</script>

<style scoped>
.key-management-page {
    background: #F6F7FB;
    min-height: 100vh;
    padding-bottom: 20px;
}

.page-header-section {
    font-size: 24px;
    font-weight: 700;
    line-height: 32px;
    color: #1A202C;
    background-color: #ffffff;
    border: 1px solid #EBECF1;
    height: 56px;
    line-height: 56px;
}

.page-header-content {
    max-width: 1440px;
    margin: 0 auto;
}

.counts-section {
    display: flex;
}

.key-management-wrapper {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0px 20px;
    background: #F6F7FB;
    min-height: 100vh;
    gap: 20px;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
}

.table-container {
    display: flex;
    flex-direction: column;
    gap: 0;
    background-color: #ffffff;
}

/* Loading state styles */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
}

.loading-spinner {
    font-size: 16px;
    color: #515666;
}

/* Pagination container styles */
.pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-top: none;
    border-radius: 0 0 6px 6px;
}

.key-list-pagination {
    margin: 0;
}
</style>
