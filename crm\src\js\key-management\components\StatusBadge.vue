<template>
    <div class="status-badge" data-testid="status-badge-container">
        <span class="status-dot" :style="{ backgroundColor: statusColor }" data-testid="status-badge-dot"></span>
        <span class="status-text" data-testid="status-badge-text">{{ statusText }}</span>
    </div>
</template>

<script>
export default {
    name: 'StatusBadge',
    langModule: 'key-management',
    props: {
        status: {
            type: String,
            required: true
        }
    },
    computed: {
        statusConfig() {
            const configs = {
                'available': {
                    text: this.$t('filters.status.available'),
                    color: '#20C472'
                },
                'checked_out': {
                    text: this.$t('filters.status.checked_out'),
                    color: '#5D51E2'
                },
                'lost': {
                    text: this.$t('filters.status.lost'),
                    color: '#F0454C'
                },
                'damaged': {
                    text: this.$t('filters.status.damaged'),
                    color: '#F0454C'
                },
                'archived': {
                    text: this.$t('filters.status.archived'),
                    color: '#C6C8D1'
                },
                // Compatible with old format
                'lost_damaged': {
                    text: this.$t('filters.status.lost_damaged'),
                    color: '#F0454C'
                }
            };
            return configs[this.status] || configs['available'];
        },
        statusText() {
            return this.statusConfig.text;
        },
        statusColor() {
            return this.statusConfig.color;
        }
    }
};
</script>

<style scoped>
.status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    opacity: 0.8;
}

.status-text {
    font-family: 'SF Pro Text';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}
</style>
