<template>
    <div class="key-statistics" :class="{ 'collapsed': !isExpanded }" data-testid="key-statistics-container">
        <!-- Loading state -->
        <div v-if="loading" class="statistics-loading">
            <div class="loading-text">{{ $t('loading.statistics') }}</div>
        </div>

        <!-- Statistics data (always show, display zero values on error) -->
        <div v-else class="statistics-data">
            <div class="stat-item" data-testid="key-statistics-item-total">
                <div class="stat-label">{{ $t('statistics.total') }}<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">
                    {{ formatNumber(statistics.total_keys || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item" data-testid="key-statistics-item-available">
                <div class="stat-label">{{ $t('statistics.available') }}<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">
                    {{ formatNumber(statistics.available || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item" data-testid="key-statistics-item-checked-out">
                <div class="stat-label">{{ $t('statistics.checked_out') }}<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">
                    {{ formatNumber(statistics.checked_out || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item" data-testid="key-statistics-item-lost">
                <div class="stat-label">{{ $t('statistics.lost') }}<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">
                    {{ formatNumber(statistics.lost || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item" data-testid="key-statistics-item-damaged">
                <div class="stat-label">{{ $t('statistics.damaged') }}<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">
                    {{ formatNumber(statistics.damaged || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item" data-testid="key-statistics-item-archived">
                <div class="stat-label">{{ $t('statistics.archived') }}<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">
                    {{ formatNumber(statistics.archived || 0) }}
                </div>
            </div>
        </div>

        <div class="icon-area" @click="toggleExpanded" data-testid="key-statistics-toggle-button">
            <i class="icon2017" :class="isExpanded ? 'icon-expand_up_02' : 'icon-expand_down_02'"></i>
        </div>
    </div>
</template>

<script>
import keyApi from '../api.js';

export default {
    name: 'KeyStatistics',
    langModule: 'key-management',
    // Remove props as statistics should not be linked to filters
    data() {
        return {
            isExpanded: true,
            statistics: {
                total_keys: 0,
                available: 0,
                checked_out: 0,
                lost: 0,
                damaged: 0,
                archived: 0
            },
            loading: false,
        };
    },
    mounted() {
        // Load statistics data only once when component is mounted
        this.loadStatistics();
    },
    methods: {
        /**
         * Load statistics data from API
         */
        async loadStatistics() {
            this.loading = true;

            try {
                // Call API without any filters (get overall statistics)
                const response = await keyApi.getKeyStatistics();

                if (response.success) {
                    this.statistics = response.data;
                }
            } catch (error) {
                // On error, keep zero values (don't show error message)
                console.error('Failed to load statistics:', error);
            } finally {
                this.loading = false;
            }
        },

        /**
         * Toggle expand/collapse state
         */
        toggleExpanded() {
            this.isExpanded = !this.isExpanded;
        },


        /**
         * Format number display
         */
        formatNumber(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        }
    }
};
</script>

<style scoped>
.key-statistics {
    display: flex;
    gap: 20px;
    padding: 0 0 0 20px;
    width: 100%; 
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 80px;
}

/* Loading state styles */
.statistics-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 20px;
}

.loading-text {
    font-family: 'SF Pro Text';
    font-size: 14px;
    color: #797E8B;
}

.statistics-data {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20px;
    padding: 20px 0;
    transition: padding 0.3s ease;
}

/* 折叠状态 */
.key-statistics.collapsed .statistics-data {
    padding: 15px 0;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    transition: all 0.3s ease;
}

/* 折叠状态下的水平布局 */
.key-statistics.collapsed .stat-item {
    flex-direction: row;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    color: #797E8B;
    transition: all 0.3s ease;
}

.stat-value {
    font-weight: 700;
    font-size: 22px;
    line-height: 1.36;
    color: #202437;
}

/* 折叠状态下的较小字体 */
.key-statistics.collapsed .stat-value {
    font-size: 14px;
    line-height: 1.43;
}

.stat-divider {
    width: 0;
    height: 100%;
    border-left: 1px solid #EBECF1;
    align-self: stretch;
}

.icon-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    background: rgba(93, 81, 226, 0.1);
    align-self: stretch;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.icon-area:hover {
    background: rgba(93, 81, 226, 0.15);
}

.icon-area .icon2017 {
    width: 10px;
    height: 10px;
    color: #5D51E2;
    font-size: 10px;
}


/* 响应式设计 */
@media (max-width: 1200px) {
    .key-statistics {
        padding: 0 0 0 15px;
    }

    .statistics-data {
        gap: 15px;
    }

    .stat-item {
        min-width: 80px;
    }

    .stat-value {
        font-size: 18px;
    }

    .key-statistics.collapsed .stat-value {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .key-statistics {
        overflow-x: auto;
        padding-bottom: 10px;
    }
    .statistics-data {
        flex-wrap: nowrap;
    }
    .stat-item {
        flex-shrink: 0;
        min-width: 100px; /* Adjust as needed */
    }
    .stat-divider {
        display: block;
    }
}
</style>
