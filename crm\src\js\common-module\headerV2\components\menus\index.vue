<template>
    <div class="menu-list main-list">
        <div class="main-ruler">
            <template v-for="(menu, index) in menuList">
                <div
                    class="list-item"
                    :key="index"
                    v-if="menu.show !== false"
                    :title="menu.title"
                    :ga-click="menu.ga"
                    :class="{
                        'curr-menu': currMenuIndex === index
                    }"
                >
                    <router-link v-if="menu.toObj" :to="menu.toObj" custom>
                        <span class="menu-item" :class="menu.icon.slice(1)" :number="menu.redPoint">
                            {{ menu.title }}
                        </span>
                    </router-link>
                    <a
                        v-else-if="menu.url && !menu.toObj"
                        class="menu-item"
                        :class="menu.icon.slice(1)"
                        :href="menu.url"
                        :number="menu.redPoint"
                    >
                        {{ menu.title }}
                    </a>
                    <span v-else @click="menu.clickHandler()" :number="menu.redPoint">
                        {{ menu.title }}
                        <img
                            class="header-beta"
                            v-if="menu.beta"
                            src="//static.chimeroi.com/servicetool-temp/icon-header-beta.png"
                            alt="beta"
                        />
                    </span>
                </div>
            </template>
        </div>
        <template v-for="(menu, index) in leftMenuList">
            <div
                class="list-item"
                :key="index"
                v-if="menu.show !== false"
                :title="menu.title"
                :ga-click="menu.ga"
                :class="{
                    'curr-menu': currentMenuName == menu.title,
                    calMenu: isCal,
                    'shorter-bottom-after': isNewLeadList(menu)
                }"
            >
                <template v-if="isNewLeadList(menu)">
                    <Select
                        class="menu-select"
                        noBorder
                        size="small"
                        :dropdownCls="`menu-select-dropdown-cls ${peopleRouteActiveCls}`"
                        rightTriangleIcon="arrow_08_down"
                        :dataSource="peopleOrSegment"
                        :value="peopleValue"
                        :clickMode="false"
                        :beforeSelect="beforeSelectPeopleItem"
                        @datachange="peopleSelectChange($event, menu)"
                    >
                        <template #label="{ label }">
                            <span @click.stop="handleClickLabel(label)">{{
                                $st("common-header", "headerv2.menu.contacts")
                            }}</span>
                        </template>
                        <template v-slot="{ option }">
                            <router-link class="menu-select-option-item" custom :to="option.toObj">
                                <i :class="`icon2017 icon-${option.icon}`"></i>
                                <span>{{ option.name }}</span>
                            </router-link>
                        </template>
                    </Select>
                </template>
                <template v-else>
                    <router-link v-if="menu.toObj" :to="menu.toObj" custom>
                        <span class="menu-item" :class="menu.icon.slice(1)" :number="menu.redPoint">
                            {{ menu.title }}
                        </span>
                    </router-link>
                    <a
                        v-else-if="!menu.toObj && menu.url"
                        class="menu-item"
                        :class="menu.icon.slice(1)"
                        :href="menu.url"
                        :number="menu.redPoint"
                    >
                        {{ menu.title }}
                    </a>
                    <span v-else @click="menu.clickHandler" :number="menu.redPoint">
                        {{ menu.title }}
                        <img
                            class="header-beta"
                            v-if="menu.beta"
                            src="//static.chimeroi.com/servicetool-temp/icon-header-beta.png"
                            alt="beta"
                        />
                    </span>
                </template>
            </div>
        </template>
        <DropDown
            :isOpen.sync="showSmallList"
            :hasArrow="false"
            :noBorder="true"
            :clickMode="false"
            dropdownCls="small-list-dropdown-container"
            v-show="rightMenuList.length"
        >
            <template #body>
                <div class="header-small-nav">
                    <div class="smallList">
                        <span class="more">{{ $st("common-header", "headerv2.menu.more") }}</span>
                        <i
                            class="icon2017"
                            :class="showSmallList ? 'icon-arrow_08_up' : 'icon-arrow_08_down'"
                        ></i>
                    </div>
                </div>
            </template>
            <template #dropdown>
                <ul class="header-menu-list">
                    <template v-for="(menu, index) in rightMenuList">
                        <li
                            class="list-item"
                            :class="isRightMenuActive(menu) ? 'active' : ''"
                            :key="index"
                            v-if="menu.show !== false"
                            :title="menu.title"
                            :ga-click="menu.ga"
                            @click="listItemClick"
                        >
                            <router-link v-if="menu.toObj" :to="menu.toObj" custom class="content">
                                <span
                                    class="icon2017"
                                    :number="menu.redPoint"
                                    :class="menu.icon"
                                ></span>
                                <span class="menu-item">{{ menu.title }}</span>
                            </router-link>
                            <a v-else-if="!menu.toObj && menu.url" :href="menu.url" class="content">
                                <span
                                    class="icon2017"
                                    :number="menu.redPoint"
                                    :class="menu.icon"
                                ></span>
                                <span class="text">{{ menu.title }}</span>
                            </a>
                            <span v-else class="content" @click="menu.clickHandler">
                                <span
                                    class="icon2017"
                                    :number="menu.redPoint"
                                    :class="menu.icon"
                                ></span>
                                <span class="text">{{ menu.title }}</span>
                            </span>
                        </li>
                    </template>
                </ul>
            </template>
        </DropDown>
    </div>
</template>
<script>
import { debounce } from "lodash";
import { components } from "common";
import { infoData } from "crm";

const { DropDown, Select } = components;

export default {
    langModule: "common-header",
    name: "Menus",
    components: {
        DropDown,
        Select
    },
    props: {
        menuList: {
            type: Array,
            default: () => []
        },
        currMenuIndex: {
            type: [String, Number],
            default: () => ""
        }
    },
    data() {
        return {
            menuCutLength: 0,
            initMenuStyle: null,
            isCal: true,

            peopleRouteActiveCls: "",
            peopleValue: "people",
            peopleOrSegment: [
                {
                    id: "people",
                    name: this.$st("common-header", "headerv2.menu.people"),
                    icon: "people_06",
                    toObj: {
                        path: "/lead/list"
                    },
                    url: "/admin/home/<USER>/list?type=all"
                },
                {
                    id: "segments",
                    name: this.$st("common-header", "headerv2.menu.segments"),
                    icon: "group_01",
                    toObj: {
                        path: "/lead/segments"
                    },
                    url: "/admin/home/<USER>/segments"
                }
            ],
            showSmallList: false
        };
    },
    computed: {
        currentMenuName() {
            return this.menuList[this.currMenuIndex]?.title || "";
        },
        showMenuList() {
            return this.menuList.filter((d) => d.show !== false);
        },
        leftMenuList() {
            return this.showMenuList.slice(0, this.showMenuList.length - this.menuCutLength);
        },
        rightMenuList() {
            const otherList = this.showMenuList.slice(
                this.showMenuList.length - this.menuCutLength
            );
            const { isLofty } = infoData.getUserInfo();
            if (otherList.some((item) => item?.type === "people") && !isLofty) {
                const segmentsItem = {
                    icon: "icon-group_01",
                    url: "/admin/home/<USER>/segments",
                    toObj: {
                        path: "/lead/segments"
                    },
                    title: this.$st("common-header", "headerv2.menu.segments"),
                    type: "segments",
                    ga: "Header_People",
                    show: true,
                    redPoint: false
                };
                otherList.splice(1, 0, segmentsItem);
            }
            return otherList;
        }
    },
    methods: {
        listItemClick() {
            this.showSmallList = false;
        },
        initMenuStyleFunc() {
            this.isCal = true;
            this.$nextTick(() => {
                let menuWidth = document.querySelector(".crm-headerV2-main")?.offsetWidth || 1440;
                let resizeWidth = menuWidth - 545;
                let tempWidth = 72;
                let reserveLength = 0;
                this.showMenuList.some((d) => {
                    let curMenuWidth = document.querySelector(
                        `.crm-headerV2-main .main-ruler .list-item[title="${d.title}"]`
                    )?.offsetWidth;
                    tempWidth += curMenuWidth + 30;
                    if (tempWidth > resizeWidth) {
                        return true;
                    }
                    reserveLength++;
                    return false;
                });
                this.menuCutLength =
                    reserveLength == this.showMenuList.length - 1
                        ? 0
                        : this.showMenuList.length - reserveLength;
                this.isCal = false;
            });
        },
        isNewLeadList(menu) {
            const { isLofty } = infoData.getUserInfo();

            return menu.name === "people" && !isLofty;
        },
        currentPageIsSegment() {
            const aboutSegmentUrls = ["/lead/segments", "/lead/segmentDetail"];
            const locationHref = window.location.href;
            return aboutSegmentUrls.some((item) => locationHref.includes(item));
        },
        beforeSelectPeopleItem({ value }) {
            if (value === "segments" && !this.currentPageIsSegment()) {
                return false;
            }
            return true;
        },
        peopleSelectChange({ value }) {
            this.peopleValue = value;
        },
        locationHrefChange() {
            const locationHref = window.location.href;
            if (this.currentPageIsSegment()) {
                this.peopleValue = "segments";
            } else {
                this.peopleValue = "people";
            }

            if (this.currentPageIsSegment() || locationHref.includes("/lead/list")) {
                this.peopleRouteActiveCls = "is-people-segment-route-dropdown-cls";
            } else {
                this.peopleRouteActiveCls = "";
            }
        },
        isRightMenuActive(menu) {
            if (this.currentPageIsSegment()) {
                return menu.type == "segments";
            }
            return this.currentMenuName == menu.title;
        },
        handleClickLabel(label) {
            if (label.includes("People")) {
                window.__crmRouter.push({
                    name: "leadList",
                    query: {
                        type: "all"
                    }
                });
            }
        }
    },
    created() {
        this.initMenuStyle = debounce(this.initMenuStyleFunc, 500, {
            trailing: true
        });

        this.$eventBus.$on("__routeChange", (data) => {
            if (data.menuName === "people") {
                this.locationHrefChange();
                this.peopleRouteActiveCls = "is-people-segment-route-dropdown-cls";
            } else {
                this.peopleValue = "people";
                this.peopleRouteActiveCls = "";
            }
        });
    },
    mounted() {
        this.$nextTick(() => {
            this.initMenuStyleFunc();
        });
        window.addEventListener("resize", () => {
            this.initMenuStyle();
        });

        this.locationHrefChange();
    }
};
</script>
<style lang="less">
.crm-header-v2.crm-header {
    .menu-list {
        display: flex;
        align-items: center;
        height: 100%;
        margin-right: auto;
        .calMenu {
            z-index: -1;
            position: absolute;
            top: -1000px;
        }
        .main-ruler {
            z-index: -1;
            position: absolute;
            top: -1000px;
            width: auto;
            display: flex;
            flex-shrink: 0;
        }
        .smallList .icon2017 {
            cursor: pointer;
            color: #b5bacc;
            margin-left: 5px;
            margin-right: 10px;
            font-size: 24px;
        }
        .small-menu-icon {
            padding: 3px 8px;
            border-radius: 5px;
            vertical-align: middle;
            &.active {
                color: #797e8b;
                background-color: #f5f5f5;
            }
        }
        .list-item {
            position: relative;
            font-weight: 500;
            font-size: 15px;
            line-height: 18px;
            margin-right: 30px;
            height: 100%;
            display: flex;
            align-items: center;
            transition: all 0.3s;
            a,
            span {
                color: var(--upgrade-header-text-Color);
                white-space: nowrap;
                height: 40px;
                line-height: 40px;
                display: inline-block;
                cursor: pointer;
            }
            .red-point::after {
                width: 5px;
                height: 5px;
                border: none;
                position: absolute;
                top: 10px;
                right: -5px;
            }
            .red-text::after {
                position: absolute;
                left: unset;
                right: -30px;
            }
            &::after {
                content: "";
                position: absolute;
                width: 0;
                height: 3px;
                background: var(--upgrade-header-underline-Color);
                left: 50%;
                bottom: 0;
                transition: all 0.3s;
            }
            &:hover {
                a,
                span {
                    color: var(--upgrade-header-underline-Color);
                }
                &::after {
                    left: 0;
                    width: 100%;
                }
            }
            &.curr-menu {
                a,
                span {
                    color: var(--upgrade-header-underline-Color);
                }
                &::after {
                    left: 0;
                    width: 100%;
                }
            }

            .header-beta {
                position: absolute;
                width: 24px;
                height: 12px;
                right: 0;
                top: 4px;
            }
            .menu-select {
                .com-dropdown-label {
                    height: 60px;
                    line-height: 60px;
                    font-weight: 500;
                    font-size: 15px;
                    padding-left: 0;
                    padding-right: 17px;
                    .com-dropdown-text {
                        height: 60px;
                        line-height: 60px;
                    }
                    .icon.right {
                        right: 0;
                        font-size: 12px;
                        color: #a0a3af;
                        margin: 0;
                    }
                    .prefix-icon-box {
                        display: none;
                    }
                }
            }
        }
        .shorter-bottom-after:hover {
            &.list-item::after {
                width: calc(100% - 20px);
            }
        }
        .shorter-bottom-after {
            &.curr-menu::after {
                width: calc(100% - 20px);
            }
        }
        .header-small-nav {
            .smallList {
                color: var(--upgrade-header-smalllist-Color);
                display: flex;
                align-items: center;
                .more {
                    cursor: pointer;
                }
                span {
                    font-size: 15px;
                    font-weight: 500;
                }
                i {
                    transform-origin: 5px center;
                    font-size: 12px;
                    transform: rotateZ(0);
                    color: #a0a3af;
                }

                &.openSync {
                    i {
                        transform: rotateZ(180deg);
                    }
                }
            }
        }
    }

    .curr-menu {
        .icon2017 {
            color: var(--primary-color);
        }
    }
}
.small-list-dropdown-container {
    transform: translateY(21px);
    border-radius: 5px;
    border: 1px solid #ebecf1;
    box-shadow: 0 2px 5px 0 rgba(0, 10, 30, 0.1);
    background-color: #fff;
    box-sizing: border-box;
    border-radius: 4px;
}
.header-menu-list {
    width: 170px;
    box-sizing: border-box;
    padding: 10px;
    margin: 0;
    .list-item {
        font-size: 14px;
        height: 38px;
        color: #515666;
        background-color: #fff;
        padding: 0 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
        .content {
            display: flex;
            align-items: center;
            height: 100%;
        }
        &:hover {
            background: #f6f7fb;
            border-radius: 4px;
            color: var(--primary-color);
            .icon2017 {
                color: var(--primary-color);
            }
        }
        .icon2017 {
            margin-right: 10px;
            font-size: 14px;
            color: #515666;
        }
        &.active {
            span {
                color: var(--primary-color);
            }
        }
    }
}

.home .list-item>.icon-chime_03,
    .people .list-item>.icon-people_04,
    .people-detail .list-item>.icon-people_04,
    .tasks .list-item>.icon-task_complete_02,
    .campaigns .list-item>.icon-campaign_01,
    .report .list-item>.icon-reporting_02,
    .listingmgmt .list-item>.icon-listing_management,
    .smartlisting .list-item>.icon-listing_management,
    .transactionmgmt .list-item>.icon-dollor,
    .transaction-detail .list-item>.icon-dollor,
    .activities .list-item>.icon-activities_04,
    .settings .list-item>.icon-settings_02,
    .website .list-item>.icon-idx,
    // .billings .list-item>.icon-Subscribe_01,
    .marketplace .list-item>.icon-marketplace_01 {
    color: var(--primary-color);
    text-shadow: 0 4px 6px rgba(var(--primary-color-rgb), 0.2);
    &:hover {
        color: var(--primary-color);
    }
}

.home .header-menu-list .list-item[title="Chime"],
.people .header-menu-list .list-item[title="People"],
.people-detail .header-menu-list .list-item[title="People"],
.tasks .header-menu-list .list-item[title="Tasks"],
.report .header-menu-list .list-item[title="Reporting"],
.activities .header-menu-list .list-item[title="Activities"],
.settings .header-menu-list .list-item[title="Settings"],
.billings .header-menu-list .list-item[title="Billing"],
.marketplace .header-menu-list .list-item[title="Marketplace"],
.listingmgmt .header-menu-list .list-item[title="Listing Discovery"],
.transactionmgmt .header-menu-list .list-item[title="Transactions"],
.transaction-detail .header-menu-list .list-item[title="Transactions"],
.website .header-menu-list .list-item[title="Website"],
.campaigns .header-menu-list .list-item[title="Campaigns"] {
    color: var(--upgrade-theme-Color);
    .icon2017 {
        color: var(--upgrade-theme-Color);
    }
}

.crm-header-v2 .header-small-nav {
    display: block;
}
.menu-select-dropdown-cls {
    width: 180px;
    .com-select-item {
        padding: 0 !important;
    }
    .com-select-item.active::before {
        display: none !important;
    }
    .com-select-list {
        padding: 10px;
        .com-select-item:hover {
            border-radius: 4px;
            background-color: #f6f7fb;
        }
    }
    .menu-select-option-item {
        display: flex;
        align-items: center;
        height: 36px;
        padding: 0 15px;
        width: 100%;
        box-sizing: border-box;
        cursor: pointer;
        .icon2017 {
            font-size: 16px;
            color: #515666;
            margin-right: 10px;
        }
        span {
            color: #515666;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
        }
    }
    .com-select-item:hover {
        background-color: #f6f7fb;
        .menu-select-option-item {
            .icon2017,
            span {
                color: var(--primary-color);
            }
        }
    }
}
.is-people-segment-route-dropdown-cls {
    .com-select-item.active {
        .menu-select-option-item {
            .icon2017,
            span {
                color: var(--primary-color);
            }
        }
    }
}
</style>
