
<template>
  <div class="property-detail">
    <Side />
    <TabBar />
    <NotePanel />
    <div class="main-content">
      <Timeline />
      <Tasks />
    </div>
  </div>
</template>

<script>
import TabBar from './components/TabBar.vue';
import Side from './components/Side.vue';
import Timeline from './components/Timeline.vue';
import Tasks from './components/Tasks.vue';
import NotePanel from './components/NotePanel.vue';

export default {
  name: 'PropertyDetail',
  components: {
    TabBar,
    Side,
    Timeline,
    Tasks,
    NotePanel,
  },
};
</script>

<style scoped>
.property-detail {
  display: grid;
  grid-template-columns: auto 1fr 1fr;
  grid-template-rows: auto auto 1fr;
  grid-template-areas: 
    "side tab-bar tab-bar"
    "side note-panel note-panel"
    "side main-content main-content";
  height: 100vh;
  gap: 16px;
}

.property-detail > * {
  min-width: 0;
}

.property-detail > :nth-child(1) { /* Side */
  grid-area: side;
}

.property-detail > :nth-child(2) { /* TabBar */
  grid-area: tab-bar;
}

.property-detail > :nth-child(3) { /* NotePanel */
  grid-area: note-panel;
}

.property-detail > :nth-child(4) { /* main-content */
  grid-area: main-content;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
