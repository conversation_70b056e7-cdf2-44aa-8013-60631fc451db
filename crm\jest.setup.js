// Jest 测试环境设置文件

// Vue Test Utils 全局配置
import { config } from "@vue/test-utils";

// 配置Vue Test Utils
config.mocks = {
    // 模拟全局方法
    $t: (key) => key, // i18n
    $tc: (key) => key,
    $te: (key) => key,
    $d: (value) => value,
    $n: (value) => value,
    $router: {
        push: jest.fn(),
        replace: jest.fn(),
        go: jest.fn(),
        back: jest.fn(),
        forward: jest.fn()
    },
    $route: {
        path: "/",
        query: {},
        params: {},
        hash: "",
        fullPath: "/",
        matched: [],
        meta: {},
        name: null
    }
};

// 全局stub组件
config.stubs = {
    "router-link": "<a><slot /></a>",
    "router-view": "<div><slot /></div>",
    "nuxt-link": "<a><slot /></a>",
    transition: "<div><slot /></div>",
    "keep-alive": "<div><slot /></div>"
};

// 配置Vue
config.silent = true;

// 模拟window对象的方法
Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
    }))
});

// 模拟localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.sessionStorage = sessionStorageMock;

// 模拟console方法 (可选，用于消除测试中的console输出)
global.console = {
    ...console,
    // 在测试中静默warn和error
    warn: jest.fn(),
    error: jest.fn()
};

// 简单的 jQuery mock
const mockJQuery = (selector) => {
    let element = null; // 支持 DOM 元素对象

    if (typeof selector === "string") {
        element = document.querySelector(selector);
    } else if (selector instanceof HTMLElement) {
        element = selector;
    } else if (selector && selector[0] instanceof HTMLElement) {
        // 兼容 $( [element] ) 或 $( $(element) )
        element = selector[0];
    } else {
        throw new SyntaxError(`${selector} is not a valid selector or element`);
    }

    return {
        text: () => element?.textContent || "",
        html: (content) => {
            if (content !== undefined) {
                if (element) {
                    element.innerHTML = content;
                }
                return mockJQuery(element);
            }
            return element?.innerHTML || "";
        },
        val: (value) => {
            if (value !== undefined) {
                if (element) {
                    element.value = value;
                }
                return mockJQuery(element);
            }
            return element?.value || "";
        },
        addClass: (className) => {
            if (element) {
                element.classList.add(className);
            }
            return mockJQuery(element);
        },
        removeClass: (className) => {
            if (element) {
                element.classList.remove(className);
            }
            return mockJQuery(element);
        },
        css: (property, value) => {
            if (!element) {
                return mockJQuery(element);
            }

            if (typeof property === "string") {
                if (value !== undefined) {
                    element.style[property] = value;
                    return mockJQuery(element);
                } else {
                    return getComputedStyle(element)[property];
                }
            } else if (typeof property === "object") {
                // 支持传对象：.css({ width: "0", height: "10px" })
                for (const key in property) {
                    if (property.hasOwnProperty(key)) {
                        element.style[key] = property[key];
                    }
                }
                return mockJQuery(element);
            }

            return mockJQuery(element);
        },
        show: () => {
            if (element) {
                element.style.display = "block";
            }
            return mockJQuery(element);
        },
        hide: () => {
            if (element) {
                element.style.display = "none";
            }
            return mockJQuery(element);
        },
        width: (value) => {
            if (value !== undefined) {
                element.style.width = value;
            }
            return element?.offsetWidth || 0;
        },
        height: (value) => {
            if (value !== undefined) {
                element.style.height = value;
            }
            return element?.offsetHeight || 0;
        },
        on: (event, handler) => {
            if (element) {
                element.addEventListener(event, handler);
            }
            return mockJQuery(element);
        },
        off: (event, handler) => {
            if (element) {
                element.removeEventListener(event, handler);
            }
            return mockJQuery(element);
        },
        delay: (ms, fn) => {
            return new Promise((resolve) => setTimeout(resolve, ms)).then(fn);
        },
        fadeOut: (ms, fn) => {
            return new Promise((resolve) => setTimeout(resolve, ms)).then(fn);
        },
        fadeIn: (ms, fn) => {
            return new Promise((resolve) => setTimeout(resolve, ms)).then(fn);
        },
        fadeToggle: (ms, fn) => {
            return new Promise((resolve) => setTimeout(resolve, ms)).then(fn);
        }
    };
};

global.$ = global.jQuery = mockJQuery;
if (typeof window !== "undefined") {
    window.$ = window.jQuery = mockJQuery;
}

// 全局测试工具函数
global.sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
