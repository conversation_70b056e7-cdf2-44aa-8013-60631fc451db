{"bd_zero": "- BD", "bd_one": "{{count}} BD", "bd_other": "{{ count }} BDs", "ba_zero": "- BA", "ba_one": "{{ count }} BA", "ba_other": "{{ count }} BA", "sqft_zero": "- SqFt", "sqft_one": "{{ countStr }} SqFt", "sqft_other": "{{ countStr }} SqFt", "upperBd": "{{count}} Bed", "upperBds": "{{count}} Beds", "upperBths_one": "{{count}} Bath", "upperBths_other": "{{count}} Baths", "upperSqft": "{{count}} SqFt", "moreCardNum": "{{count}} units here", "basic": {"boostListing": "Boost Listing", "viewOnWebsite": "View on Website", "createTextCode": "Set Text Code", "matchedBuyer": "Matched Clients", "editPocket": "Edit Manual Listing", "viewInLettings": "View in Lettings", "editListing": "Edit Listing", "deleteListing": "Delete Listing", "deletePocket": "Delete Manual Listing", "editPocketListingDisabled": "You are not authorized to edit this listing.", "deletePocketListingDisabled": "You are not authorized to delete this listing.", "sendToLoftyWorks": "Send to LoftyWorks", "viewInLoftyWorks": "View in LoftyWorks", "disabledViewInLoftyWorks": "Property can be synchronized to LoftyWorks only by listing agent.", "addBuyerCompensation": "Add Buyer Compensation", "editBuyerCompensation": "Edit Buyer Compensation", "notListingAgent": "Property can be synchronized to LoftyWorks only by listing agent."}, "tabs": {"media": "Media", "details": "Details", "map": "Map", "school": "Schools", "history": "History", "mortgage": "Mortgage"}, "detail": {"openHouse": "Open House", "propertyDesc": "Property Description", "keyDetails": "Key Details", "location": "Location", "rooms": "Rooms", "interior": "Interior", "exterior": "Exterior", "building": "Building", "others": "Others", "priceDetails": "Price Details", "utilities": "Utilities"}, "history": {"date": "Date", "event": "Event", "price": "Price", "today": "Today", "source": "source"}, "mapModule": {"walkScore": "Walk Score", "carDependent": "Car-Dependent", "similarHomes": "Similar Homes For Sale", "viewMore": "View More"}, "mortgage": {"estimatedPayment": "Estimated Payment", "perMonth": "Per Month", "principal": "Principal & Interest", "propertyTax": "Property Tax", "councilTax": "Council Tax", "hoaFees": "HOA Fees", "adjustParameters": "Adjust Parameters", "downPayment": "Down Payment", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "loanTerm": "<PERSON>an <PERSON>", "interestRate": "Interest Rate", "year": "year", "homeInsurance": "Home Insurance", "month": "month", "tips": "Mortgage values are calculated by LoftyWorks and are for illustration purposes only, accuracy is not guaranteed.", "reset": "Reset", "calculate": "Calculate", "10year": "10-Year Fixed", "15year": "15-Year Fixed", "20year": "20-Year Fixed", "25year": "25-Year Fixed", "30year": "30-Year Fixed", "40year": "40-Year Fixed"}, "school": {"schoolDataProvided": "Schools data provided by <a class=\"disclaimer\" href=\"https://www.greatschools.org/\" target=\"_blank\" rel=\"noopener\">GreatSchools</a>.", "servingProperty": "Serving This Property", "elementary": "Elementary", "middle": "Middle", "high": "High", "viewMore": "View More", "detail": {"admission": "Admission", "level": "Level", "rating": "Rating", "library": "Library", "yes": "YES", "no": "NO", "phone": "Phone", "expenditure": "Expenditure", "address": "Address", "ratio": "<PERSON><PERSON>"}}, "unpublished": "Draft RLS", "nonRlsListing": "Non-RLS", "nonRls": "Draft Non-RLS", "rls": "RLS", "luxVT": "Premiere", "participantOnly": "Participant Only", "participantOnlyTips": "The listing is Participant Only Listing. Retransmission, redistribution or copying of this listing information is strictly prohibited.", "addCompensation": "Add Buyer Compensation", "editCompensation": "Edit Buyer Compensation", "compensationAmount": "Compensation Amount:", "buyerCompensationTip": "I understand that I am responsible for ensuring that my use of this feature is compliant with MLS regulations.", "compensationLabel": "Compensation Label:", "amountPlaceHolder": "Enter a percentage, flat fee, or description...", "noChange": "No Changes Made", "hasChange": "Save Successfully", "compensationLabelDisabledTip": "Please contact your team owner or admin for edit access.", "isEsUploading": "Listing is still uploading. Please wait and try again later."}