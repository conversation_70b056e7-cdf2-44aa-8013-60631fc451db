<template>
  <div class="tab-bar">
    <div class="tabs">
      <a href="#" class="active">Overview</a>
      <a href="#">Marketing</a>
      <a href="#">Keys</a>
      <a href="#">Viewings</a>
      <a href="#">Buyers</a>
      <a href="#">Offers</a>
      <a href="#">Sales Progression</a>
      <a href="#">More <img src="https://via.placeholder.com/150" alt="arrow down" /></a>
    </div>
    <div class="right">
      <button class="publish-button">Publish to Portals</button>
      <button class="more-button"><img src="https://via.placeholder.com/150" alt="more icon" /></button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabBar',
};
</script>

<style scoped>
.tab-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  border-bottom: 1px solid #e1e2e6;
}
.tabs {
  display: flex;
  align-items: center;
  gap: 30px;
}
.tabs a {
  text-decoration: none;
  color: #797e8b;
  font-size: 14px;
  font-weight: 510;
  padding: 10px 0;
}
.tabs a.active {
  color: #5d51e2;
  border-bottom: 2px solid #5d51e2;
}
.right {
  display: flex;
  align-items: center;
  gap: 10px;
}
.publish-button {
  background-color: #5d51e2;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 20px;
  font-size: 14px;
  cursor: pointer;
}
.more-button {
  background-color: #fff;
  border: 1px solid #e1e2e6;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
}
</style>