---
description: 
globs: 
alwaysApply: false
---
**规则名称：** Vue 组件测试生成器

**触发词：** `@generate-test`

**规则内容：**

```
你是一个 Vue 组件测试生成专家。当用户提供 Vue 组件文件路径时，你需要：

1. **分析组件文件**：读取并分析提供的 Vue 组件文件
2. **生成测试文件路径**：根据以下规则确定测试文件位置
   - 移除 `src/js/` 前缀
   - 将 `.vue` 或 `.js` 替换为 `.test.js`
   - 如果是 `index.vue`，使用父目录名作为测试文件名
   - 测试文件放在 `test/` 目录下对应位置

3. **路径映射示例**：
   ```
   crm/src/js/usersetting/billing/index.vue → crm/test/usersetting/billing.test.js
   crm/src/js/components/UserProfile.vue → crm/test/components/UserProfile.test.js
   ```

4. **生成完整测试文件**，包含以下结构：

### 基础模板结构
```javascript
import { mount, createLocalVue } from "@vue/test-utils";
import [ComponentName] from "@/[相对路径]";

// Mock 外部依赖
jest.mock("crm", () => ({
    crmUtils: {
        sendAjax: jest.fn()
    }
}));

jest.mock("common", () => ({
    utils: {
        toast: jest.fn()
    }
}));

describe("[ComponentName]", () => {
    let wrapper;
    let localVue;
    let mockSendAjax;
    let mockToast;

    beforeEach(() => {
        localVue = createLocalVue();
        
        // 获取 mock 函数
        const { crmUtils } = jest.requireMock("crm");
        const { utils } = jest.requireMock("common");
        mockSendAjax = crmUtils.sendAjax;
        mockToast = utils.toast;

        // 重置 mocks
        mockSendAjax.mockClear();
        mockToast.mockClear();

        wrapper = mount([ComponentName], {
            localVue,
            mocks: {
                $t: (key) => {
                    const translations = {
                        // 根据组件中的 $t 调用添加翻译映射
                    };
                    return translations[key] || key;
                }
            }
        });
    });

    afterEach(() => {
        if (wrapper) {
            wrapper.destroy();
        }
    });

    // 测试用例...
});
```

5. **必须包含的测试类别**（根据组件实际情况选择性包含）：

   a) **组件渲染测试**
   b) **初始状态测试**  
   c) **用户交互测试**（如果有表单/按钮等）
   d) **方法测试**（为每个 methods 中的方法生成测试）
   e) **API 交互测试**（如果检测到 HTTP 请求）
   f) **Props 测试**（如果组件接受 props）
   g) **Computed 属性测试**（如果有计算属性）
   h) **集成测试**（完整的用户交互流程）

6. **组件分析要点**：
   - 提取主要的 CSS 类名用于 DOM 查询
   - 识别表单元素（input、select、checkbox等）
   - 识别按钮和可点击元素
   - 分析数据流和状态变化
   - 检测 API 调用模式

7. **命名规范**：
   - 组件名：`index.vue` → `BillingComponent`（使用父目录名）
   - 组件名：`UserProfile.vue` → `UserProfileComponent`
   - 测试描述使用中文，清晰描述测试目的

8. **Mock 配置**：
   - 自动检测 import 语句并生成对应 mock
   - 常见依赖的标准 mock 配置
   - 确保每个测试前重置 mock 状态

9. **测试覆盖要求**：
   - 至少包含 15-25 个测试用例
   - 覆盖主要的用户交互路径
   - 包含正常和异常情况的测试
   - 确保重要业务逻辑都有测试

10. **输出格式**：
    - 直接输出完整的测试文件代码
    - 代码应该可以直接运行
    - 包含必要的注释说明
    - 遵循项目的代码风格

请根据提供的组件文件路径，分析组件并生成完整的测试文件。
```