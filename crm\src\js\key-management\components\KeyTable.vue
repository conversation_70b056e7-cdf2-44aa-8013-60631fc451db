<template>
    <div class="key-table-wrapper" data-testid="key-table-container">
        <DataTable :data="tableData" class="key-management-table" :noBorder="false" data-testid="key-table">
            <DataTableColumn :label="$t('table.columns.property_address')" :widthRatio="3" :minWidth="440">
                <template slot-scope="{ data }" data-testid="key-table-column-template">
                    <PropertyItem :property="data" />
                </template>
            </DataTableColumn>

            <DataTableColumn :label="$t('table.columns.key_code')" :width="160">
                <template slot-scope="{ data }" data-testid="key-table-column-template">
                    <span class="key-code">{{ data.key_code || data.keyCode }}</span>
                </template>
            </DataTableColumn>

            <DataTableColumn :label="$t('table.columns.holder')" :width="200">
                <template slot-scope="{ data }" data-testid="key-table-column-template">
                    <span class="holder-name">{{ data.current_holder_name || data.holder || $t('table.no_holder')
                        }}</span>
                </template>
            </DataTableColumn>

            <DataTableColumn :label="$t('table.columns.status')" :width="200">
                <template slot-scope="{ data }" data-testid="key-table-column-template">
                    <StatusBadge :status="data.status" />
                </template>
            </DataTableColumn>

            <DataTableColumn :label="$t('table.columns.last_activity')" :width="200">
                <template slot-scope="{ data }" data-testid="key-table-column-template">
                    <span class="last-access">{{ formatLastActivity(data.last_activity_at || data.lastAccess) }}</span>
                </template>
            </DataTableColumn>
        </DataTable>
    </div>
</template>

<script>
import { components } from 'common';
import PropertyItem from './PropertyItem.vue';
import StatusBadge from './StatusBadge.vue';

const { DataTable } = components;

export default {
    name: 'KeyTable',
    langModule: 'key-management',
    components: {
        DataTable,
        PropertyItem,
        StatusBadge
    },
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        tableData() {
            return this.data;
        }
    },
    methods: {
        formatLastActivity(dateString) {
            if (!dateString || dateString === 'Never') {
                return this.$t('table.never');
            }

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    return dateString; // If not a valid date, return original string
                }

                // Format as "1 Jul 2025" format
                const options = {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric'
                };
                return date.toLocaleDateString('en-GB', options);
            } catch (error) {
                return dateString;
            }
        }
    }
};
</script>

<style scoped>
.key-table-wrapper {
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-top: none;
    border-radius: 0px 0px 6px 6px;
}

.key-management-table {
    width: 100%;
}

.key-code,
.holder-name,
.last-access {
    font-family: 'SF Pro Text';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}

/* 表头样式 */
.key-management-table :deep(.com-datatable-header-wrap) {
    background: #FFFFFF;
}

.key-management-table :deep(.com-datatable-header-wrap .table-head-cell) {
    font-family: 'SF Pro';
    font-weight: 700;
    font-size: 14px;
    line-height: 1.43;
    color: #202437;
    padding: 12px 0;
}

/* 表格行样式 */
.key-management-table :deep(.table-body-row) {
    border-bottom: 1px solid #EBECF1;
}

.key-management-table :deep(.table-body-row:last-child) {
    border-bottom: none;
}

.key-management-table :deep(.table-body-cell) {
    padding: 20px 0;
    vertical-align: middle;
}

/* 第一列特殊样式 */
.key-management-table :deep(.table-body-cell:first-child) {
    padding-left: 20px;
}

/* 最后一列特殊样式 */
.key-management-table :deep(.table-body-cell:last-child) {
    padding-right: 20px;
}
</style>
