.DS_Store

.idea
.idea/*
.idea*
\.idea*
.vscode
.vscode/*
.vscode*
\.vscode*
.cache
crm/proxy.cookie.txt
node_modules
node_modules_old
node_modules_new
crm/src/plugins/ckeditor/samples/old/assets/*
.test_cache
.crm_webpack_cache
scripts/output.txt
crm/dist/*
email-editor/dist/*
crm/language/lang-csv/*
crm/language/lang-flat-json/*
crm/language/cookie.txt
language/log
crm/backoffice/dist
crm/publicOnline/views/*
crm-alert/dist/*
crm-broker/dist/*
crm-leadScore/dist/*
crm-isa/dist/*
crm-excel-web/dist/*
crm-packages/dist/*
crm-packages/ai-package/dist/*
crm-packages/language/lang-csv/*
crm-packages/language/lang-flat-json/*
crm-admin/dist/*
crm-cuadmin/dist/*
crm-mobile/dist/*
vendor-oauth/dist/*
workspace.xml
source-map-test.json
crm/src/css/*
site/npm-debug.log
crm-broker/src/css/*
design-center/docs/*
design-center/lib/
design-center/dist
design-center/src/components/imagemap/test
crm-leadScore/src/css/*
payment/static/*
.swp
version-control/build
merged-node_modules/pnpm-lock.yaml
merged-node_modules/package.json
jsp-version-output-test
assetsTest
card.jsp.tar.gz
card.static.tar.gz
site.jsp.tar.gz
site.static.tar.gz
payment.static.tar.gz
payment.jsp.tar.gz
forgot.jsp.tar.gz
forgot.static.tar.gz
home.jsp.tar.gz
home.static.tar.gz
home.all.tar.gz
pp-site.jsp.tar.gz
pp-site.static.tar.gz
card-collect.jsp.tar.gz
card-collect.static.tar.gz
gather.jsp.tar.gz
gather.static.tar.gz
ad-cma.static.tar.gz
ad-cma.jsp.tar.gz
webpack
webpack-dev-server
source-map-test-2.json
new-site.jsp.tar.gz
new-site.static.tar.gz
source-map-test-1.json
assets2
dist.tar.gz
gg-site.jsp.tar.gz
gg-site.static.tar.gz
newhome.static.tar.gz
newhome.jsp.tar.gz
ad-evaluation.jsp.tar.gz
ad-evaluation.static.tar.gz
pp-site-2.jsp.tar.gz
pp-site-2.static.tar.gz
pp-site-1.jsp.tar.gz
pp-site-1.static.tar.gz
site.all.tar.gz
jsp-version-output-test-1
jsp-version-output-test-2
vow.static.tar.gz
vow.jsp.tar.gz
coverage
payment-admin
payment-page
ad-admin
ecrm-web
crm/deployTest_win.sh
stats.json
vendor-oauth/dist/*
*.bak
crm/language/log
trans-annotation/*
**/uploadJs/**
