# SonarCube 项目配置
sonar.projectKey=crm-frontend
sonar.projectName=CRM Frontend
sonar.projectVersion=3.0.0

# 源代码路径
sonar.sources=src
sonar.sourceEncoding=UTF-8

# 测试文件路径
sonar.tests=test
sonar.test.inclusions=**/*.test.js,**/*.spec.js

# JavaScript/Vue 覆盖率报告路径
sonar.javascript.lcov.reportPaths=coverage/lcov.info

# 排除不需要分析的文件
sonar.exclusions=**/node_modules/**,**/dist/**,**/lib/**,**/coverage/**,**/*.min.js,**/plugins/**

# 测试覆盖率排除项
sonar.coverage.exclusions=**/*.test.js,**/*.spec.js,**/__mock__/**,**/test/**

# JavaScript 分析器配置
sonar.javascript.file.suffixes=.js,.jsx,.vue 